/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated by scripts/releases/set-version.js
 */

#pragma once

#include <cstdint>
#include <string_view>

#define REACT_NATIVE_VERSION_MAJOR 0
#define REACT_NATIVE_VERSION_MINOR 79
#define REACT_NATIVE_VERSION_PATCH 5

namespace facebook::react {

constexpr struct {
  int32_t Major = 0;
  int32_t Minor = 79;
  int32_t Patch = 5;
  std::string_view Prerelease = "";
} ReactNativeVersion;

} // namespace facebook::react
