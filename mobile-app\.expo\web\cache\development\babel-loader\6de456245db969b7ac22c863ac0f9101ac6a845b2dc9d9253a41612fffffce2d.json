{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"onScroll\", \"onTouchMove\", \"onWheel\", \"scrollEnabled\", \"scrollEventThrottle\", \"showsHorizontalScrollIndicator\", \"showsVerticalScrollIndicator\", \"style\"];\nimport * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nimport useMergeRefs from \"../../modules/useMergeRefs\";\nfunction normalizeScrollEvent(e) {\n  return {\n    nativeEvent: {\n      contentOffset: {\n        get x() {\n          return e.target.scrollLeft;\n        },\n        get y() {\n          return e.target.scrollTop;\n        }\n      },\n      contentSize: {\n        get height() {\n          return e.target.scrollHeight;\n        },\n        get width() {\n          return e.target.scrollWidth;\n        }\n      },\n      layoutMeasurement: {\n        get height() {\n          return e.target.offsetHeight;\n        },\n        get width() {\n          return e.target.offsetWidth;\n        }\n      }\n    },\n    timeStamp: Date.now()\n  };\n}\nfunction shouldEmitScrollEvent(lastTick, eventThrottle) {\n  var timeSinceLastTick = Date.now() - lastTick;\n  return eventThrottle > 0 && timeSinceLastTick >= eventThrottle;\n}\nvar ScrollViewBase = React.forwardRef(function (props, forwardedRef) {\n  var onScroll = props.onScroll,\n    onTouchMove = props.onTouchMove,\n    onWheel = props.onWheel,\n    _props$scrollEnabled = props.scrollEnabled,\n    scrollEnabled = _props$scrollEnabled === void 0 ? true : _props$scrollEnabled,\n    _props$scrollEventThr = props.scrollEventThrottle,\n    scrollEventThrottle = _props$scrollEventThr === void 0 ? 0 : _props$scrollEventThr,\n    showsHorizontalScrollIndicator = props.showsHorizontalScrollIndicator,\n    showsVerticalScrollIndicator = props.showsVerticalScrollIndicator,\n    style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var scrollState = React.useRef({\n    isScrolling: false,\n    scrollLastTick: 0\n  });\n  var scrollTimeout = React.useRef(null);\n  var scrollRef = React.useRef(null);\n  function createPreventableScrollHandler(handler) {\n    return function (e) {\n      if (scrollEnabled) {\n        if (handler) {\n          handler(e);\n        }\n      }\n    };\n  }\n  function handleScroll(e) {\n    e.stopPropagation();\n    if (e.target === scrollRef.current) {\n      e.persist();\n      if (scrollTimeout.current != null) {\n        clearTimeout(scrollTimeout.current);\n      }\n      scrollTimeout.current = setTimeout(function () {\n        handleScrollEnd(e);\n      }, 100);\n      if (scrollState.current.isScrolling) {\n        if (shouldEmitScrollEvent(scrollState.current.scrollLastTick, scrollEventThrottle)) {\n          handleScrollTick(e);\n        }\n      } else {\n        handleScrollStart(e);\n      }\n    }\n  }\n  function handleScrollStart(e) {\n    scrollState.current.isScrolling = true;\n    handleScrollTick(e);\n  }\n  function handleScrollTick(e) {\n    scrollState.current.scrollLastTick = Date.now();\n    if (onScroll) {\n      onScroll(normalizeScrollEvent(e));\n    }\n  }\n  function handleScrollEnd(e) {\n    scrollState.current.isScrolling = false;\n    if (onScroll) {\n      onScroll(normalizeScrollEvent(e));\n    }\n  }\n  var hideScrollbar = showsHorizontalScrollIndicator === false || showsVerticalScrollIndicator === false;\n  return React.createElement(View, _extends({}, rest, {\n    onScroll: handleScroll,\n    onTouchMove: createPreventableScrollHandler(onTouchMove),\n    onWheel: createPreventableScrollHandler(onWheel),\n    ref: useMergeRefs(scrollRef, forwardedRef),\n    style: [style, !scrollEnabled && styles.scrollDisabled, hideScrollbar && styles.hideScrollbar]\n  }));\n});\nvar styles = StyleSheet.create({\n  scrollDisabled: {\n    overflowX: 'hidden',\n    overflowY: 'hidden',\n    touchAction: 'none'\n  },\n  hideScrollbar: {\n    scrollbarWidth: 'none'\n  }\n});\nexport default ScrollViewBase;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "StyleSheet", "View", "useMergeRefs", "normalizeScrollEvent", "e", "nativeEvent", "contentOffset", "x", "target", "scrollLeft", "y", "scrollTop", "contentSize", "height", "scrollHeight", "width", "scrollWidth", "layoutMeasurement", "offsetHeight", "offsetWidth", "timeStamp", "Date", "now", "shouldEmitScrollEvent", "lastTick", "eventThrottle", "timeSinceLastTick", "ScrollViewBase", "forwardRef", "props", "forwardedRef", "onScroll", "onTouchMove", "onWheel", "_props$scrollEnabled", "scrollEnabled", "_props$scrollEventThr", "scrollEventThrottle", "showsHorizontalScrollIndicator", "showsVerticalScrollIndicator", "style", "rest", "scrollState", "useRef", "isScrolling", "scrollLastTick", "scrollTimeout", "scrollRef", "createPreventableScrollHandler", "handler", "handleScroll", "stopPropagation", "current", "persist", "clearTimeout", "setTimeout", "handleScrollEnd", "handleScrollTick", "handleScrollStart", "hideScrollbar", "createElement", "ref", "styles", "scrollDisabled", "create", "overflowX", "overflowY", "touchAction", "scrollbarWidth"], "sources": ["C:/laragon/www/mobile-app/node_modules/react-native-web/dist/exports/ScrollView/ScrollViewBase.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"onScroll\", \"onTouchMove\", \"onWheel\", \"scrollEnabled\", \"scrollEventThrottle\", \"showsHorizontalScrollIndicator\", \"showsVerticalScrollIndicator\", \"style\"];\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nimport useMergeRefs from '../../modules/useMergeRefs';\nfunction normalizeScrollEvent(e) {\n  return {\n    nativeEvent: {\n      contentOffset: {\n        get x() {\n          return e.target.scrollLeft;\n        },\n        get y() {\n          return e.target.scrollTop;\n        }\n      },\n      contentSize: {\n        get height() {\n          return e.target.scrollHeight;\n        },\n        get width() {\n          return e.target.scrollWidth;\n        }\n      },\n      layoutMeasurement: {\n        get height() {\n          return e.target.offsetHeight;\n        },\n        get width() {\n          return e.target.offsetWidth;\n        }\n      }\n    },\n    timeStamp: Date.now()\n  };\n}\nfunction shouldEmitScrollEvent(lastTick, eventThrottle) {\n  var timeSinceLastTick = Date.now() - lastTick;\n  return eventThrottle > 0 && timeSinceLastTick >= eventThrottle;\n}\n\n/**\n * Encapsulates the Web-specific scroll throttling and disabling logic\n */\nvar ScrollViewBase = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var onScroll = props.onScroll,\n    onTouchMove = props.onTouchMove,\n    onWheel = props.onWheel,\n    _props$scrollEnabled = props.scrollEnabled,\n    scrollEnabled = _props$scrollEnabled === void 0 ? true : _props$scrollEnabled,\n    _props$scrollEventThr = props.scrollEventThrottle,\n    scrollEventThrottle = _props$scrollEventThr === void 0 ? 0 : _props$scrollEventThr,\n    showsHorizontalScrollIndicator = props.showsHorizontalScrollIndicator,\n    showsVerticalScrollIndicator = props.showsVerticalScrollIndicator,\n    style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var scrollState = React.useRef({\n    isScrolling: false,\n    scrollLastTick: 0\n  });\n  var scrollTimeout = React.useRef(null);\n  var scrollRef = React.useRef(null);\n  function createPreventableScrollHandler(handler) {\n    return e => {\n      if (scrollEnabled) {\n        if (handler) {\n          handler(e);\n        }\n      }\n    };\n  }\n  function handleScroll(e) {\n    e.stopPropagation();\n    if (e.target === scrollRef.current) {\n      e.persist();\n      // A scroll happened, so the scroll resets the scrollend timeout.\n      if (scrollTimeout.current != null) {\n        clearTimeout(scrollTimeout.current);\n      }\n      scrollTimeout.current = setTimeout(() => {\n        handleScrollEnd(e);\n      }, 100);\n      if (scrollState.current.isScrolling) {\n        // Scroll last tick may have changed, check if we need to notify\n        if (shouldEmitScrollEvent(scrollState.current.scrollLastTick, scrollEventThrottle)) {\n          handleScrollTick(e);\n        }\n      } else {\n        // Weren't scrolling, so we must have just started\n        handleScrollStart(e);\n      }\n    }\n  }\n  function handleScrollStart(e) {\n    scrollState.current.isScrolling = true;\n    handleScrollTick(e);\n  }\n  function handleScrollTick(e) {\n    scrollState.current.scrollLastTick = Date.now();\n    if (onScroll) {\n      onScroll(normalizeScrollEvent(e));\n    }\n  }\n  function handleScrollEnd(e) {\n    scrollState.current.isScrolling = false;\n    if (onScroll) {\n      onScroll(normalizeScrollEvent(e));\n    }\n  }\n  var hideScrollbar = showsHorizontalScrollIndicator === false || showsVerticalScrollIndicator === false;\n  return /*#__PURE__*/React.createElement(View, _extends({}, rest, {\n    onScroll: handleScroll,\n    onTouchMove: createPreventableScrollHandler(onTouchMove),\n    onWheel: createPreventableScrollHandler(onWheel),\n    ref: useMergeRefs(scrollRef, forwardedRef),\n    style: [style, !scrollEnabled && styles.scrollDisabled, hideScrollbar && styles.hideScrollbar]\n  }));\n});\n\n// Chrome doesn't support e.preventDefault in this case; touch-action must be\n// used to disable scrolling.\n// https://developers.google.com/web/updates/2017/01/scrolling-intervention\nvar styles = StyleSheet.create({\n  scrollDisabled: {\n    overflowX: 'hidden',\n    overflowY: 'hidden',\n    touchAction: 'none'\n  },\n  hideScrollbar: {\n    scrollbarWidth: 'none'\n  }\n});\nexport default ScrollViewBase;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,gCAAgC,EAAE,8BAA8B,EAAE,OAAO,CAAC;AAUzK,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,OAAOC,YAAY;AACnB,SAASC,oBAAoBA,CAACC,CAAC,EAAE;EAC/B,OAAO;IACLC,WAAW,EAAE;MACXC,aAAa,EAAE;QACb,IAAIC,CAACA,CAAA,EAAG;UACN,OAAOH,CAAC,CAACI,MAAM,CAACC,UAAU;QAC5B,CAAC;QACD,IAAIC,CAACA,CAAA,EAAG;UACN,OAAON,CAAC,CAACI,MAAM,CAACG,SAAS;QAC3B;MACF,CAAC;MACDC,WAAW,EAAE;QACX,IAAIC,MAAMA,CAAA,EAAG;UACX,OAAOT,CAAC,CAACI,MAAM,CAACM,YAAY;QAC9B,CAAC;QACD,IAAIC,KAAKA,CAAA,EAAG;UACV,OAAOX,CAAC,CAACI,MAAM,CAACQ,WAAW;QAC7B;MACF,CAAC;MACDC,iBAAiB,EAAE;QACjB,IAAIJ,MAAMA,CAAA,EAAG;UACX,OAAOT,CAAC,CAACI,MAAM,CAACU,YAAY;QAC9B,CAAC;QACD,IAAIH,KAAKA,CAAA,EAAG;UACV,OAAOX,CAAC,CAACI,MAAM,CAACW,WAAW;QAC7B;MACF;IACF,CAAC;IACDC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;EACtB,CAAC;AACH;AACA,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,aAAa,EAAE;EACtD,IAAIC,iBAAiB,GAAGL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGE,QAAQ;EAC7C,OAAOC,aAAa,GAAG,CAAC,IAAIC,iBAAiB,IAAID,aAAa;AAChE;AAKA,IAAIE,cAAc,GAAgB5B,KAAK,CAAC6B,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAC1E,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,oBAAoB,GAAGL,KAAK,CAACM,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,oBAAoB;IAC7EE,qBAAqB,GAAGP,KAAK,CAACQ,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAClFE,8BAA8B,GAAGT,KAAK,CAACS,8BAA8B;IACrEC,4BAA4B,GAAGV,KAAK,CAACU,4BAA4B;IACjEC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,IAAI,GAAG5C,6BAA6B,CAACgC,KAAK,EAAE/B,SAAS,CAAC;EACxD,IAAI4C,WAAW,GAAG3C,KAAK,CAAC4C,MAAM,CAAC;IAC7BC,WAAW,EAAE,KAAK;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,IAAIC,aAAa,GAAG/C,KAAK,CAAC4C,MAAM,CAAC,IAAI,CAAC;EACtC,IAAII,SAAS,GAAGhD,KAAK,CAAC4C,MAAM,CAAC,IAAI,CAAC;EAClC,SAASK,8BAA8BA,CAACC,OAAO,EAAE;IAC/C,OAAO,UAAA7C,CAAC,EAAI;MACV,IAAI+B,aAAa,EAAE;QACjB,IAAIc,OAAO,EAAE;UACXA,OAAO,CAAC7C,CAAC,CAAC;QACZ;MACF;IACF,CAAC;EACH;EACA,SAAS8C,YAAYA,CAAC9C,CAAC,EAAE;IACvBA,CAAC,CAAC+C,eAAe,CAAC,CAAC;IACnB,IAAI/C,CAAC,CAACI,MAAM,KAAKuC,SAAS,CAACK,OAAO,EAAE;MAClChD,CAAC,CAACiD,OAAO,CAAC,CAAC;MAEX,IAAIP,aAAa,CAACM,OAAO,IAAI,IAAI,EAAE;QACjCE,YAAY,CAACR,aAAa,CAACM,OAAO,CAAC;MACrC;MACAN,aAAa,CAACM,OAAO,GAAGG,UAAU,CAAC,YAAM;QACvCC,eAAe,CAACpD,CAAC,CAAC;MACpB,CAAC,EAAE,GAAG,CAAC;MACP,IAAIsC,WAAW,CAACU,OAAO,CAACR,WAAW,EAAE;QAEnC,IAAIrB,qBAAqB,CAACmB,WAAW,CAACU,OAAO,CAACP,cAAc,EAAER,mBAAmB,CAAC,EAAE;UAClFoB,gBAAgB,CAACrD,CAAC,CAAC;QACrB;MACF,CAAC,MAAM;QAELsD,iBAAiB,CAACtD,CAAC,CAAC;MACtB;IACF;EACF;EACA,SAASsD,iBAAiBA,CAACtD,CAAC,EAAE;IAC5BsC,WAAW,CAACU,OAAO,CAACR,WAAW,GAAG,IAAI;IACtCa,gBAAgB,CAACrD,CAAC,CAAC;EACrB;EACA,SAASqD,gBAAgBA,CAACrD,CAAC,EAAE;IAC3BsC,WAAW,CAACU,OAAO,CAACP,cAAc,GAAGxB,IAAI,CAACC,GAAG,CAAC,CAAC;IAC/C,IAAIS,QAAQ,EAAE;MACZA,QAAQ,CAAC5B,oBAAoB,CAACC,CAAC,CAAC,CAAC;IACnC;EACF;EACA,SAASoD,eAAeA,CAACpD,CAAC,EAAE;IAC1BsC,WAAW,CAACU,OAAO,CAACR,WAAW,GAAG,KAAK;IACvC,IAAIb,QAAQ,EAAE;MACZA,QAAQ,CAAC5B,oBAAoB,CAACC,CAAC,CAAC,CAAC;IACnC;EACF;EACA,IAAIuD,aAAa,GAAGrB,8BAA8B,KAAK,KAAK,IAAIC,4BAA4B,KAAK,KAAK;EACtG,OAAoBxC,KAAK,CAAC6D,aAAa,CAAC3D,IAAI,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE6C,IAAI,EAAE;IAC/DV,QAAQ,EAAEmB,YAAY;IACtBlB,WAAW,EAAEgB,8BAA8B,CAAChB,WAAW,CAAC;IACxDC,OAAO,EAAEe,8BAA8B,CAACf,OAAO,CAAC;IAChD4B,GAAG,EAAE3D,YAAY,CAAC6C,SAAS,EAAEjB,YAAY,CAAC;IAC1CU,KAAK,EAAE,CAACA,KAAK,EAAE,CAACL,aAAa,IAAI2B,MAAM,CAACC,cAAc,EAAEJ,aAAa,IAAIG,MAAM,CAACH,aAAa;EAC/F,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAKF,IAAIG,MAAM,GAAG9D,UAAU,CAACgE,MAAM,CAAC;EAC7BD,cAAc,EAAE;IACdE,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE;EACf,CAAC;EACDR,aAAa,EAAE;IACbS,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AACF,eAAezC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}