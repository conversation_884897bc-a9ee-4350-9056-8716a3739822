{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/EvilIcons.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/EvilIcons.json\";\nexport default createIconSet(glyphMap, 'evilicons', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\@expo\\vector-icons\\src\\EvilIcons.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/EvilIcons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/EvilIcons.json';\n\nexport default createIconSet(glyphMap, 'evilicons', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,WAAW,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}