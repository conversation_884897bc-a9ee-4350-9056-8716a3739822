{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nexport default function (config, expoFontName, expoAssetId) {\n  var glyphMap = {};\n  config.icons.forEach(function (icon) {\n    icon.properties.name.split(/\\s*,\\s*/g).forEach(function (name) {\n      glyphMap[name] = icon.properties.code;\n    });\n  });\n  var fontFamily = expoFontName || config.preferences.fontPref.metadata.fontFamily;\n  return createIconSet(glyphMap, fontFamily, expoAssetId || `${fontFamily}.ttf`);\n}", "map": {"version": 3, "names": ["createIconSet", "config", "expoFontName", "expoAssetId", "glyphMap", "icons", "for<PERSON>ach", "icon", "properties", "name", "split", "code", "fontFamily", "preferences", "fontPref", "metadata"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\@expo\\vector-icons\\src\\createIconSetFromIcoMoon.ts"], "sourcesContent": ["import createIconSet from \"./createIconSet\";\n\nexport default function (config, expoFontName, expoAssetId) {\n  const glyphMap = {};\n  config.icons.forEach(icon => {\n    icon.properties.name.split(/\\s*,\\s*/g).forEach(name => {\n      glyphMap[name] = icon.properties.code;\n    });\n  });\n\n  const fontFamily =\n    expoFontName || config.preferences.fontPref.metadata.fontFamily;\n\n  return createIconSet<string, string>(\n    glyphMap,\n    fontFamily,\n    expoAssetId || `${fontFamily}.ttf`\n  );\n}\n"], "mappings": "AAAA,OAAOA,aAAa;AAEpB,eAAc,UAAWC,MAAM,EAAEC,YAAY,EAAEC,WAAW;EACxD,IAAMC,QAAQ,GAAG,EAAE;EACnBH,MAAM,CAACI,KAAK,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAG;IAC1BA,IAAI,CAACC,UAAU,CAACC,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC,CAACJ,OAAO,CAAC,UAAAG,IAAI,EAAG;MACpDL,QAAQ,CAACK,IAAI,CAAC,GAAGF,IAAI,CAACC,UAAU,CAACG,IAAI;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,IAAMC,UAAU,GACdV,YAAY,IAAID,MAAM,CAACY,WAAW,CAACC,QAAQ,CAACC,QAAQ,CAACH,UAAU;EAEjE,OAAOZ,aAAa,CAClBI,QAAQ,EACRQ,UAAU,EACVT,WAAW,IAAI,GAAGS,UAAU,MAAM,CACnC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}