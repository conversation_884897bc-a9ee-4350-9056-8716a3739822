{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Card, Title, Paragraph, Button, Searchbar, Text, Avatar, Chip, ActivityIndicator } from 'react-native-paper';\nimport { Ionicons } from '@expo/vector-icons';\nimport { theme } from \"../theme/theme\";\nimport { apiService } from \"../services/api\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function UsersScreen() {\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    users = _useState2[0],\n    setUsers = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    filteredUsers = _useState4[0],\n    setFilteredUsers = _useState4[1];\n  var _useState5 = useState(''),\n    _useState6 = _slicedToArray(_useState5, 2),\n    searchQuery = _useState6[0],\n    setSearchQuery = _useState6[1];\n  var _useState7 = useState(true),\n    _useState8 = _slicedToArray(_useState7, 2),\n    loading = _useState8[0],\n    setLoading = _useState8[1];\n  var _useState9 = useState(false),\n    _useState0 = _slicedToArray(_useState9, 2),\n    refreshing = _useState0[0],\n    setRefreshing = _useState0[1];\n  var _useState1 = useState(null),\n    _useState10 = _slicedToArray(_useState1, 2),\n    currentUser = _useState10[0],\n    setCurrentUser = _useState10[1];\n  useEffect(function () {\n    loadData();\n  }, []);\n  useEffect(function () {\n    filterUsers();\n  }, [searchQuery, users]);\n  var loadData = function () {\n    var _ref = _asyncToGenerator(function* () {\n      try {\n        var user = yield apiService.getCurrentUser();\n        if (user) {\n          setCurrentUser(user);\n          var response = yield apiService.getUsers(user.id);\n          if (response.success && response.data) {\n            setUsers(response.data);\n          }\n        }\n      } catch (error) {\n        Alert.alert('Erro', 'Erro ao carregar usuários');\n      } finally {\n        setLoading(false);\n      }\n    });\n    return function loadData() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  var filterUsers = function filterUsers() {\n    if (!searchQuery) {\n      setFilteredUsers(users);\n    } else {\n      var filtered = users.filter(function (user) {\n        return user.nome.toLowerCase().includes(searchQuery.toLowerCase()) || user.email.toLowerCase().includes(searchQuery.toLowerCase());\n      });\n      setFilteredUsers(filtered);\n    }\n  };\n  var onRefresh = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      setRefreshing(true);\n      yield loadData();\n      setRefreshing(false);\n    });\n    return function onRefresh() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var handleUpdateBalance = function handleUpdateBalance(user, tipo) {\n    Alert.prompt(`${tipo === 'credito' ? 'Adicionar' : 'Remover'} Saldo`, `Digite o valor para ${tipo === 'credito' ? 'adicionar ao' : 'remover do'} saldo de ${user.nome}:`, [{\n      text: 'Cancelar',\n      style: 'cancel'\n    }, {\n      text: 'Confirmar',\n      onPress: function () {\n        var _onPress = _asyncToGenerator(function* (value) {\n          if (value && !isNaN(Number(value))) {\n            var valor = parseFloat(value);\n            var response = yield apiService.updateUserBalance(user.id, valor, tipo);\n            if (response.success) {\n              Alert.alert('Sucesso', `Saldo ${tipo === 'credito' ? 'adicionado' : 'removido'} com sucesso!`);\n              loadData();\n            } else {\n              Alert.alert('Erro', response.error || 'Erro ao atualizar saldo');\n            }\n          } else {\n            Alert.alert('Erro', 'Valor inválido');\n          }\n        });\n        function onPress(_x) {\n          return _onPress.apply(this, arguments);\n        }\n        return onPress;\n      }()\n    }], 'plain-text', '', 'numeric');\n  };\n  var formatCurrency = function formatCurrency(value) {\n    return new Intl.NumberFormat('pt-BR', {\n      style: 'currency',\n      currency: 'BRL'\n    }).format(value);\n  };\n  var getStatusColor = function getStatusColor(status) {\n    switch (status) {\n      case 'ativo':\n        return theme.colors.success;\n      case 'inativo':\n        return theme.colors.warning;\n      case 'bloqueado':\n        return theme.colors.error;\n      default:\n        return theme.colors.disabled;\n    }\n  };\n  var renderUser = function renderUser(_ref3) {\n    var item = _ref3.item;\n    return _jsx(Card, {\n      style: styles.userCard,\n      children: _jsxs(Card.Content, {\n        children: [_jsxs(View, {\n          style: styles.userHeader,\n          children: [_jsx(Avatar.Text, {\n            size: 50,\n            label: item.nome.substring(0, 2).toUpperCase(),\n            style: {\n              backgroundColor: theme.colors.primary\n            }\n          }), _jsxs(View, {\n            style: styles.userInfo,\n            children: [_jsx(Title, {\n              style: styles.userName,\n              children: item.nome\n            }), _jsx(Paragraph, {\n              style: styles.userEmail,\n              children: item.email\n            }), _jsxs(View, {\n              style: styles.userMeta,\n              children: [_jsx(Chip, {\n                icon: \"account\",\n                style: [styles.statusChip, {\n                  backgroundColor: getStatusColor(item.status)\n                }],\n                textStyle: styles.statusChipText,\n                children: item.status.toUpperCase()\n              }), _jsx(Text, {\n                style: styles.userBalance,\n                children: formatCurrency(item.saldo)\n              })]\n            })]\n          })]\n        }), _jsxs(View, {\n          style: styles.userActions,\n          children: [_jsx(Button, {\n            mode: \"contained\",\n            icon: \"plus\",\n            style: [styles.actionButton, {\n              backgroundColor: theme.colors.success\n            }],\n            onPress: function onPress() {\n              return handleUpdateBalance(item, 'credito');\n            },\n            compact: true,\n            children: \"Adicionar\"\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            icon: \"minus\",\n            style: styles.actionButton,\n            onPress: function onPress() {\n              return handleUpdateBalance(item, 'debito');\n            },\n            compact: true,\n            children: \"Remover\"\n          })]\n        })]\n      })\n    });\n  };\n  if (loading) {\n    return _jsxs(View, {\n      style: styles.loadingContainer,\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: theme.colors.primary\n      }), _jsx(Text, {\n        style: styles.loadingText,\n        children: \"Carregando usu\\xE1rios...\"\n      })]\n    });\n  }\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Searchbar, {\n        placeholder: \"Buscar usu\\xE1rios...\",\n        onChangeText: setSearchQuery,\n        value: searchQuery,\n        style: styles.searchbar\n      }), _jsx(View, {\n        style: styles.statsHeader,\n        children: _jsxs(Text, {\n          style: styles.statsText,\n          children: [filteredUsers.length, \" usu\\xE1rio\", filteredUsers.length !== 1 ? 's' : '']\n        })\n      })]\n    }), _jsx(FlatList, {\n      data: filteredUsers,\n      renderItem: renderUser,\n      keyExtractor: function keyExtractor(item) {\n        return item.id.toString();\n      },\n      contentContainerStyle: styles.listContainer,\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      ListEmptyComponent: _jsxs(View, {\n        style: styles.emptyContainer,\n        children: [_jsx(Ionicons, {\n          name: \"people-outline\",\n          size: 64,\n          color: theme.colors.disabled\n        }), _jsx(Text, {\n          style: styles.emptyText,\n          children: \"Nenhum usu\\xE1rio encontrado\"\n        })]\n      })\n    })]\n  });\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: theme.colors.background\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  loadingText: {\n    marginTop: 16,\n    color: theme.colors.text\n  },\n  header: {\n    padding: 16,\n    backgroundColor: theme.colors.surface,\n    elevation: 2\n  },\n  searchbar: {\n    marginBottom: 12\n  },\n  statsHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  statsText: {\n    color: theme.colors.onSurface,\n    fontSize: 14\n  },\n  listContainer: {\n    padding: 16\n  },\n  userCard: {\n    marginBottom: 12,\n    elevation: 2\n  },\n  userHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 16\n  },\n  userInfo: {\n    flex: 1,\n    marginLeft: 16\n  },\n  userName: {\n    fontSize: 18,\n    marginBottom: 4\n  },\n  userEmail: {\n    fontSize: 14,\n    color: theme.colors.onSurface,\n    marginBottom: 8\n  },\n  userMeta: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between'\n  },\n  statusChip: {\n    height: 24\n  },\n  statusChipText: {\n    color: '#fff',\n    fontSize: 10\n  },\n  userBalance: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: theme.colors.primary\n  },\n  userActions: {\n    flexDirection: 'row',\n    justifyContent: 'space-around'\n  },\n  actionButton: {\n    flex: 1,\n    marginHorizontal: 8\n  },\n  emptyContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    paddingVertical: 64\n  },\n  emptyText: {\n    marginTop: 16,\n    fontSize: 16,\n    color: theme.colors.disabled,\n    textAlign: 'center'\n  }\n});", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "StyleSheet", "FlatList", "RefreshControl", "<PERSON><PERSON>", "Card", "Title", "Paragraph", "<PERSON><PERSON>", "Searchbar", "Text", "Avatar", "Chip", "ActivityIndicator", "Ionicons", "theme", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "UsersScreen", "_useState", "_useState2", "_slicedToArray", "users", "setUsers", "_useState3", "_useState4", "filteredUsers", "setFilteredUsers", "_useState5", "_useState6", "searchQuery", "setSearch<PERSON>uery", "_useState7", "_useState8", "loading", "setLoading", "_useState9", "_useState0", "refreshing", "setRefreshing", "_useState1", "_useState10", "currentUser", "setCurrentUser", "loadData", "filterUsers", "_ref", "_asyncToGenerator", "user", "getCurrentUser", "response", "getUsers", "id", "success", "data", "error", "alert", "apply", "arguments", "filtered", "filter", "nome", "toLowerCase", "includes", "email", "onRefresh", "_ref2", "handleUpdateBalance", "tipo", "prompt", "text", "style", "onPress", "_onPress", "value", "isNaN", "Number", "valor", "parseFloat", "updateUserBalance", "_x", "formatCurrency", "Intl", "NumberFormat", "currency", "format", "getStatusColor", "status", "colors", "warning", "disabled", "renderUser", "_ref3", "item", "styles", "userCard", "children", "Content", "userHeader", "size", "label", "substring", "toUpperCase", "backgroundColor", "primary", "userInfo", "userName", "userEmail", "userMeta", "icon", "statusChip", "textStyle", "statusChipText", "userBalance", "saldo", "userActions", "mode", "actionButton", "compact", "loadingContainer", "color", "loadingText", "container", "header", "placeholder", "onChangeText", "searchbar", "statsHeader", "statsText", "length", "renderItem", "keyExtractor", "toString", "contentContainerStyle", "listContainer", "refreshControl", "ListEmptyComponent", "emptyContainer", "name", "emptyText", "create", "flex", "background", "justifyContent", "alignItems", "marginTop", "padding", "surface", "elevation", "marginBottom", "flexDirection", "onSurface", "fontSize", "marginLeft", "height", "fontWeight", "marginHorizontal", "paddingVertical", "textAlign"], "sources": ["C:/laragon/www/mobile-app/src/screens/UsersScreen.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  StyleSheet,\n  FlatList,\n  RefreshControl,\n  Alert,\n} from 'react-native';\nimport {\n  Card,\n  Title,\n  Paragraph,\n  Button,\n  Searchbar,\n  Text,\n  Avatar,\n  Chip,\n  FAB,\n  ActivityIndicator,\n} from 'react-native-paper';\nimport { Ionicons } from '@expo/vector-icons';\nimport { theme } from '../theme/theme';\nimport { apiService, User } from '../services/api';\n\nexport default function UsersScreen() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  useEffect(() => {\n    filterUsers();\n  }, [searchQuery, users]);\n\n  const loadData = async () => {\n    try {\n      const user = await apiService.getCurrentUser();\n      if (user) {\n        setCurrentUser(user);\n        const response = await apiService.getUsers(user.id);\n        if (response.success && response.data) {\n          setUsers(response.data);\n        }\n      }\n    } catch (error) {\n      Alert.alert('Erro', 'Erro ao carregar usuários');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterUsers = () => {\n    if (!searchQuery) {\n      setFilteredUsers(users);\n    } else {\n      const filtered = users.filter(user =>\n        user.nome.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        user.email.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredUsers(filtered);\n    }\n  };\n\n  const onRefresh = async () => {\n    setRefreshing(true);\n    await loadData();\n    setRefreshing(false);\n  };\n\n  const handleUpdateBalance = (user: User, tipo: 'credito' | 'debito') => {\n    Alert.prompt(\n      `${tipo === 'credito' ? 'Adicionar' : 'Remover'} Saldo`,\n      `Digite o valor para ${tipo === 'credito' ? 'adicionar ao' : 'remover do'} saldo de ${user.nome}:`,\n      [\n        { text: 'Cancelar', style: 'cancel' },\n        {\n          text: 'Confirmar',\n          onPress: async (value) => {\n            if (value && !isNaN(Number(value))) {\n              const valor = parseFloat(value);\n              const response = await apiService.updateUserBalance(user.id, valor, tipo);\n              \n              if (response.success) {\n                Alert.alert('Sucesso', `Saldo ${tipo === 'credito' ? 'adicionado' : 'removido'} com sucesso!`);\n                loadData();\n              } else {\n                Alert.alert('Erro', response.error || 'Erro ao atualizar saldo');\n              }\n            } else {\n              Alert.alert('Erro', 'Valor inválido');\n            }\n          }\n        }\n      ],\n      'plain-text',\n      '',\n      'numeric'\n    );\n  };\n\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('pt-BR', {\n      style: 'currency',\n      currency: 'BRL',\n    }).format(value);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ativo': return theme.colors.success;\n      case 'inativo': return theme.colors.warning;\n      case 'bloqueado': return theme.colors.error;\n      default: return theme.colors.disabled;\n    }\n  };\n\n  const renderUser = ({ item }: { item: User }) => (\n    <Card style={styles.userCard}>\n      <Card.Content>\n        <View style={styles.userHeader}>\n          <Avatar.Text\n            size={50}\n            label={item.nome.substring(0, 2).toUpperCase()}\n            style={{ backgroundColor: theme.colors.primary }}\n          />\n          <View style={styles.userInfo}>\n            <Title style={styles.userName}>{item.nome}</Title>\n            <Paragraph style={styles.userEmail}>{item.email}</Paragraph>\n            <View style={styles.userMeta}>\n              <Chip\n                icon=\"account\"\n                style={[styles.statusChip, { backgroundColor: getStatusColor(item.status) }]}\n                textStyle={styles.statusChipText}\n              >\n                {item.status.toUpperCase()}\n              </Chip>\n              <Text style={styles.userBalance}>\n                {formatCurrency(item.saldo)}\n              </Text>\n            </View>\n          </View>\n        </View>\n\n        <View style={styles.userActions}>\n          <Button\n            mode=\"contained\"\n            icon=\"plus\"\n            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}\n            onPress={() => handleUpdateBalance(item, 'credito')}\n            compact\n          >\n            Adicionar\n          </Button>\n          <Button\n            mode=\"outlined\"\n            icon=\"minus\"\n            style={styles.actionButton}\n            onPress={() => handleUpdateBalance(item, 'debito')}\n            compact\n          >\n            Remover\n          </Button>\n        </View>\n      </Card.Content>\n    </Card>\n  );\n\n  if (loading) {\n    return (\n      <View style={styles.loadingContainer}>\n        <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n        <Text style={styles.loadingText}>Carregando usuários...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <View style={styles.container}>\n      <View style={styles.header}>\n        <Searchbar\n          placeholder=\"Buscar usuários...\"\n          onChangeText={setSearchQuery}\n          value={searchQuery}\n          style={styles.searchbar}\n        />\n        <View style={styles.statsHeader}>\n          <Text style={styles.statsText}>\n            {filteredUsers.length} usuário{filteredUsers.length !== 1 ? 's' : ''}\n          </Text>\n        </View>\n      </View>\n\n      <FlatList\n        data={filteredUsers}\n        renderItem={renderUser}\n        keyExtractor={(item) => item.id.toString()}\n        contentContainerStyle={styles.listContainer}\n        refreshControl={\n          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />\n        }\n        ListEmptyComponent={\n          <View style={styles.emptyContainer}>\n            <Ionicons name=\"people-outline\" size={64} color={theme.colors.disabled} />\n            <Text style={styles.emptyText}>Nenhum usuário encontrado</Text>\n          </View>\n        }\n      />\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: theme.colors.background,\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  loadingText: {\n    marginTop: 16,\n    color: theme.colors.text,\n  },\n  header: {\n    padding: 16,\n    backgroundColor: theme.colors.surface,\n    elevation: 2,\n  },\n  searchbar: {\n    marginBottom: 12,\n  },\n  statsHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n  },\n  statsText: {\n    color: theme.colors.onSurface,\n    fontSize: 14,\n  },\n  listContainer: {\n    padding: 16,\n  },\n  userCard: {\n    marginBottom: 12,\n    elevation: 2,\n  },\n  userHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 16,\n  },\n  userInfo: {\n    flex: 1,\n    marginLeft: 16,\n  },\n  userName: {\n    fontSize: 18,\n    marginBottom: 4,\n  },\n  userEmail: {\n    fontSize: 14,\n    color: theme.colors.onSurface,\n    marginBottom: 8,\n  },\n  userMeta: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n  },\n  statusChip: {\n    height: 24,\n  },\n  statusChipText: {\n    color: '#fff',\n    fontSize: 10,\n  },\n  userBalance: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: theme.colors.primary,\n  },\n  userActions: {\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n  },\n  actionButton: {\n    flex: 1,\n    marginHorizontal: 8,\n  },\n  emptyContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    paddingVertical: 64,\n  },\n  emptyText: {\n    marginTop: 16,\n    fontSize: 16,\n    color: theme.colors.disabled,\n    textAlign: 'center',\n  },\n});\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,cAAA;AAAA,OAAAC,KAAA;AAQnD,SACEC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,IAAI,EAEJC,iBAAiB,QACZ,oBAAoB;AAC3B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,KAAK;AACd,SAASC,UAAU;AAAgC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEnD,eAAe,SAASC,WAAWA,CAAA,EAAG;EACpC,IAAAC,SAAA,GAA0BxB,QAAQ,CAAS,EAAE,CAAC;IAAAyB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAvCG,KAAK,GAAAF,UAAA;IAAEG,QAAQ,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAA0C7B,QAAQ,CAAS,EAAE,CAAC;IAAA8B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvDE,aAAa,GAAAD,UAAA;IAAEE,gBAAgB,GAAAF,UAAA;EACtC,IAAAG,UAAA,GAAsCjC,QAAQ,CAAC,EAAE,CAAC;IAAAkC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA3CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAClC,IAAAG,UAAA,GAA8BrC,QAAQ,CAAC,IAAI,CAAC;IAAAsC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAoCzC,QAAQ,CAAC,KAAK,CAAC;IAAA0C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAAsC7C,QAAQ,CAAc,IAAI,CAAC;IAAA8C,WAAA,GAAApB,cAAA,CAAAmB,UAAA;IAA1DE,WAAW,GAAAD,WAAA;IAAEE,cAAc,GAAAF,WAAA;EAElC7C,SAAS,CAAC,YAAM;IACdgD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAENhD,SAAS,CAAC,YAAM;IACdiD,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACf,WAAW,EAAER,KAAK,CAAC,CAAC;EAExB,IAAMsB,QAAQ;IAAA,IAAAE,IAAA,GAAAC,iBAAA,CAAG,aAAY;MAC3B,IAAI;QACF,IAAMC,IAAI,SAASnC,UAAU,CAACoC,cAAc,CAAC,CAAC;QAC9C,IAAID,IAAI,EAAE;UACRL,cAAc,CAACK,IAAI,CAAC;UACpB,IAAME,QAAQ,SAASrC,UAAU,CAACsC,QAAQ,CAACH,IAAI,CAACI,EAAE,CAAC;UACnD,IAAIF,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;YACrC/B,QAAQ,CAAC2B,QAAQ,CAACI,IAAI,CAAC;UACzB;QACF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdtD,KAAK,CAACuD,KAAK,CAAC,MAAM,EAAE,2BAA2B,CAAC;MAClD,CAAC,SAAS;QACRrB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBAfKS,QAAQA,CAAA;MAAA,OAAAE,IAAA,CAAAW,KAAA,OAAAC,SAAA;IAAA;EAAA,GAeb;EAED,IAAMb,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxB,IAAI,CAACf,WAAW,EAAE;MAChBH,gBAAgB,CAACL,KAAK,CAAC;IACzB,CAAC,MAAM;MACL,IAAMqC,QAAQ,GAAGrC,KAAK,CAACsC,MAAM,CAAC,UAAAZ,IAAI;QAAA,OAChCA,IAAI,CAACa,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,WAAW,CAACgC,WAAW,CAAC,CAAC,CAAC,IAC3Dd,IAAI,CAACgB,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,WAAW,CAACgC,WAAW,CAAC,CAAC,CAAC;MAAA,CAC9D,CAAC;MACDnC,gBAAgB,CAACgC,QAAQ,CAAC;IAC5B;EACF,CAAC;EAED,IAAMM,SAAS;IAAA,IAAAC,KAAA,GAAAnB,iBAAA,CAAG,aAAY;MAC5BR,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMK,QAAQ,CAAC,CAAC;MAChBL,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAAA,gBAJK0B,SAASA,CAAA;MAAA,OAAAC,KAAA,CAAAT,KAAA,OAAAC,SAAA;IAAA;EAAA,GAId;EAED,IAAMS,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAInB,IAAU,EAAEoB,IAA0B,EAAK;IACtEnE,KAAK,CAACoE,MAAM,CACV,GAAGD,IAAI,KAAK,SAAS,GAAG,WAAW,GAAG,SAAS,QAAQ,EACvD,uBAAuBA,IAAI,KAAK,SAAS,GAAG,cAAc,GAAG,YAAY,aAAapB,IAAI,CAACa,IAAI,GAAG,EAClG,CACE;MAAES,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAS,CAAC,EACrC;MACED,IAAI,EAAE,WAAW;MACjBE,OAAO;QAAA,IAAAC,QAAA,GAAA1B,iBAAA,CAAE,WAAO2B,KAAK,EAAK;UACxB,IAAIA,KAAK,IAAI,CAACC,KAAK,CAACC,MAAM,CAACF,KAAK,CAAC,CAAC,EAAE;YAClC,IAAMG,KAAK,GAAGC,UAAU,CAACJ,KAAK,CAAC;YAC/B,IAAMxB,QAAQ,SAASrC,UAAU,CAACkE,iBAAiB,CAAC/B,IAAI,CAACI,EAAE,EAAEyB,KAAK,EAAET,IAAI,CAAC;YAEzE,IAAIlB,QAAQ,CAACG,OAAO,EAAE;cACpBpD,KAAK,CAACuD,KAAK,CAAC,SAAS,EAAE,SAASY,IAAI,KAAK,SAAS,GAAG,YAAY,GAAG,UAAU,eAAe,CAAC;cAC9FxB,QAAQ,CAAC,CAAC;YACZ,CAAC,MAAM;cACL3C,KAAK,CAACuD,KAAK,CAAC,MAAM,EAAEN,QAAQ,CAACK,KAAK,IAAI,yBAAyB,CAAC;YAClE;UACF,CAAC,MAAM;YACLtD,KAAK,CAACuD,KAAK,CAAC,MAAM,EAAE,gBAAgB,CAAC;UACvC;QACF,CAAC;QAAA,SAdDgB,OAAOA,CAAAQ,EAAA;UAAA,OAAAP,QAAA,CAAAhB,KAAA,OAAAC,SAAA;QAAA;QAAA,OAAPc,OAAO;MAAA;IAeT,CAAC,CACF,EACD,YAAY,EACZ,EAAE,EACF,SACF,CAAC;EACH,CAAC;EAED,IAAMS,cAAc,GAAG,SAAjBA,cAAcA,CAAIP,KAAa,EAAK;IACxC,OAAO,IAAIQ,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCZ,KAAK,EAAE,UAAU;MACjBa,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC;EAClB,CAAC;EAED,IAAMY,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,MAAc,EAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO3E,KAAK,CAAC4E,MAAM,CAACnC,OAAO;MACzC,KAAK,SAAS;QAAE,OAAOzC,KAAK,CAAC4E,MAAM,CAACC,OAAO;MAC3C,KAAK,WAAW;QAAE,OAAO7E,KAAK,CAAC4E,MAAM,CAACjC,KAAK;MAC3C;QAAS,OAAO3C,KAAK,CAAC4E,MAAM,CAACE,QAAQ;IACvC;EACF,CAAC;EAED,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,KAAA;IAAA,IAAMC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAAA,OACxB9E,IAAA,CAACb,IAAI;MAACqE,KAAK,EAAEuB,MAAM,CAACC,QAAS;MAAAC,QAAA,EAC3B/E,KAAA,CAACf,IAAI,CAAC+F,OAAO;QAAAD,QAAA,GACX/E,KAAA,CAACpB,IAAI;UAAC0E,KAAK,EAAEuB,MAAM,CAACI,UAAW;UAAAF,QAAA,GAC7BjF,IAAA,CAACP,MAAM,CAACD,IAAI;YACV4F,IAAI,EAAE,EAAG;YACTC,KAAK,EAAEP,IAAI,CAAChC,IAAI,CAACwC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE;YAC/C/B,KAAK,EAAE;cAAEgC,eAAe,EAAE3F,KAAK,CAAC4E,MAAM,CAACgB;YAAQ;UAAE,CAClD,CAAC,EACFvF,KAAA,CAACpB,IAAI;YAAC0E,KAAK,EAAEuB,MAAM,CAACW,QAAS;YAAAT,QAAA,GAC3BjF,IAAA,CAACZ,KAAK;cAACoE,KAAK,EAAEuB,MAAM,CAACY,QAAS;cAAAV,QAAA,EAAEH,IAAI,CAAChC;YAAI,CAAQ,CAAC,EAClD9C,IAAA,CAACX,SAAS;cAACmE,KAAK,EAAEuB,MAAM,CAACa,SAAU;cAAAX,QAAA,EAAEH,IAAI,CAAC7B;YAAK,CAAY,CAAC,EAC5D/C,KAAA,CAACpB,IAAI;cAAC0E,KAAK,EAAEuB,MAAM,CAACc,QAAS;cAAAZ,QAAA,GAC3BjF,IAAA,CAACN,IAAI;gBACHoG,IAAI,EAAC,SAAS;gBACdtC,KAAK,EAAE,CAACuB,MAAM,CAACgB,UAAU,EAAE;kBAAEP,eAAe,EAAEjB,cAAc,CAACO,IAAI,CAACN,MAAM;gBAAE,CAAC,CAAE;gBAC7EwB,SAAS,EAAEjB,MAAM,CAACkB,cAAe;gBAAAhB,QAAA,EAEhCH,IAAI,CAACN,MAAM,CAACe,WAAW,CAAC;cAAC,CACtB,CAAC,EACPvF,IAAA,CAACR,IAAI;gBAACgE,KAAK,EAAEuB,MAAM,CAACmB,WAAY;gBAAAjB,QAAA,EAC7Bf,cAAc,CAACY,IAAI,CAACqB,KAAK;cAAC,CACvB,CAAC;YAAA,CACH,CAAC;UAAA,CACH,CAAC;QAAA,CACH,CAAC,EAEPjG,KAAA,CAACpB,IAAI;UAAC0E,KAAK,EAAEuB,MAAM,CAACqB,WAAY;UAAAnB,QAAA,GAC9BjF,IAAA,CAACV,MAAM;YACL+G,IAAI,EAAC,WAAW;YAChBP,IAAI,EAAC,MAAM;YACXtC,KAAK,EAAE,CAACuB,MAAM,CAACuB,YAAY,EAAE;cAAEd,eAAe,EAAE3F,KAAK,CAAC4E,MAAM,CAACnC;YAAQ,CAAC,CAAE;YACxEmB,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQL,mBAAmB,CAAC0B,IAAI,EAAE,SAAS,CAAC;YAAA,CAAC;YACpDyB,OAAO;YAAAtB,QAAA,EACR;UAED,CAAQ,CAAC,EACTjF,IAAA,CAACV,MAAM;YACL+G,IAAI,EAAC,UAAU;YACfP,IAAI,EAAC,OAAO;YACZtC,KAAK,EAAEuB,MAAM,CAACuB,YAAa;YAC3B7C,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQL,mBAAmB,CAAC0B,IAAI,EAAE,QAAQ,CAAC;YAAA,CAAC;YACnDyB,OAAO;YAAAtB,QAAA,EACR;UAED,CAAQ,CAAC;QAAA,CACL,CAAC;MAAA,CACK;IAAC,CACX,CAAC;EAAA,CACR;EAED,IAAI9D,OAAO,EAAE;IACX,OACEjB,KAAA,CAACpB,IAAI;MAAC0E,KAAK,EAAEuB,MAAM,CAACyB,gBAAiB;MAAAvB,QAAA,GACnCjF,IAAA,CAACL,iBAAiB;QAACyF,IAAI,EAAC,OAAO;QAACqB,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAACgB;MAAQ,CAAE,CAAC,EAC/DzF,IAAA,CAACR,IAAI;QAACgE,KAAK,EAAEuB,MAAM,CAAC2B,WAAY;QAAAzB,QAAA,EAAC;MAAsB,CAAM,CAAC;IAAA,CAC1D,CAAC;EAEX;EAEA,OACE/E,KAAA,CAACpB,IAAI;IAAC0E,KAAK,EAAEuB,MAAM,CAAC4B,SAAU;IAAA1B,QAAA,GAC5B/E,KAAA,CAACpB,IAAI;MAAC0E,KAAK,EAAEuB,MAAM,CAAC6B,MAAO;MAAA3B,QAAA,GACzBjF,IAAA,CAACT,SAAS;QACRsH,WAAW,EAAC,uBAAoB;QAChCC,YAAY,EAAE9F,cAAe;QAC7B2C,KAAK,EAAE5C,WAAY;QACnByC,KAAK,EAAEuB,MAAM,CAACgC;MAAU,CACzB,CAAC,EACF/G,IAAA,CAAClB,IAAI;QAAC0E,KAAK,EAAEuB,MAAM,CAACiC,WAAY;QAAA/B,QAAA,EAC9B/E,KAAA,CAACV,IAAI;UAACgE,KAAK,EAAEuB,MAAM,CAACkC,SAAU;UAAAhC,QAAA,GAC3BtE,aAAa,CAACuG,MAAM,EAAC,aAAQ,EAACvG,aAAa,CAACuG,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;QAAA,CAChE;MAAC,CACH,CAAC;IAAA,CACH,CAAC,EAEPlH,IAAA,CAAChB,QAAQ;MACPuD,IAAI,EAAE5B,aAAc;MACpBwG,UAAU,EAAEvC,UAAW;MACvBwC,YAAY,EAAE,SAAdA,YAAYA,CAAGtC,IAAI;QAAA,OAAKA,IAAI,CAACzC,EAAE,CAACgF,QAAQ,CAAC,CAAC;MAAA,CAAC;MAC3CC,qBAAqB,EAAEvC,MAAM,CAACwC,aAAc;MAC5CC,cAAc,EACZxH,IAAA,CAACf,cAAc;QAACsC,UAAU,EAAEA,UAAW;QAAC2B,SAAS,EAAEA;MAAU,CAAE,CAChE;MACDuE,kBAAkB,EAChBvH,KAAA,CAACpB,IAAI;QAAC0E,KAAK,EAAEuB,MAAM,CAAC2C,cAAe;QAAAzC,QAAA,GACjCjF,IAAA,CAACJ,QAAQ;UAAC+H,IAAI,EAAC,gBAAgB;UAACvC,IAAI,EAAE,EAAG;UAACqB,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAACE;QAAS,CAAE,CAAC,EAC1E3E,IAAA,CAACR,IAAI;UAACgE,KAAK,EAAEuB,MAAM,CAAC6C,SAAU;UAAA3C,QAAA,EAAC;QAAyB,CAAM,CAAC;MAAA,CAC3D;IACP,CACF,CAAC;EAAA,CACE,CAAC;AAEX;AAEA,IAAMF,MAAM,GAAGhG,UAAU,CAAC8I,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,IAAI,EAAE,CAAC;IACPtC,eAAe,EAAE3F,KAAK,CAAC4E,MAAM,CAACsD;EAChC,CAAC;EACDvB,gBAAgB,EAAE;IAChBsB,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDvB,WAAW,EAAE;IACXwB,SAAS,EAAE,EAAE;IACbzB,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAAClB;EACtB,CAAC;EACDqD,MAAM,EAAE;IACNuB,OAAO,EAAE,EAAE;IACX3C,eAAe,EAAE3F,KAAK,CAAC4E,MAAM,CAAC2D,OAAO;IACrCC,SAAS,EAAE;EACb,CAAC;EACDtB,SAAS,EAAE;IACTuB,YAAY,EAAE;EAChB,CAAC;EACDtB,WAAW,EAAE;IACXuB,aAAa,EAAE,KAAK;IACpBP,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDhB,SAAS,EAAE;IACTR,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAAC+D,SAAS;IAC7BC,QAAQ,EAAE;EACZ,CAAC;EACDlB,aAAa,EAAE;IACbY,OAAO,EAAE;EACX,CAAC;EACDnD,QAAQ,EAAE;IACRsD,YAAY,EAAE,EAAE;IAChBD,SAAS,EAAE;EACb,CAAC;EACDlD,UAAU,EAAE;IACVoD,aAAa,EAAE,KAAK;IACpBN,UAAU,EAAE,QAAQ;IACpBK,YAAY,EAAE;EAChB,CAAC;EACD5C,QAAQ,EAAE;IACRoC,IAAI,EAAE,CAAC;IACPY,UAAU,EAAE;EACd,CAAC;EACD/C,QAAQ,EAAE;IACR8C,QAAQ,EAAE,EAAE;IACZH,YAAY,EAAE;EAChB,CAAC;EACD1C,SAAS,EAAE;IACT6C,QAAQ,EAAE,EAAE;IACZhC,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAAC+D,SAAS;IAC7BF,YAAY,EAAE;EAChB,CAAC;EACDzC,QAAQ,EAAE;IACR0C,aAAa,EAAE,KAAK;IACpBN,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE;EAClB,CAAC;EACDjC,UAAU,EAAE;IACV4C,MAAM,EAAE;EACV,CAAC;EACD1C,cAAc,EAAE;IACdQ,KAAK,EAAE,MAAM;IACbgC,QAAQ,EAAE;EACZ,CAAC;EACDvC,WAAW,EAAE;IACXuC,QAAQ,EAAE,EAAE;IACZG,UAAU,EAAE,MAAM;IAClBnC,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAACgB;EACtB,CAAC;EACDW,WAAW,EAAE;IACXmC,aAAa,EAAE,KAAK;IACpBP,cAAc,EAAE;EAClB,CAAC;EACD1B,YAAY,EAAE;IACZwB,IAAI,EAAE,CAAC;IACPe,gBAAgB,EAAE;EACpB,CAAC;EACDnB,cAAc,EAAE;IACdI,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBa,eAAe,EAAE;EACnB,CAAC;EACDlB,SAAS,EAAE;IACTM,SAAS,EAAE,EAAE;IACbO,QAAQ,EAAE,EAAE;IACZhC,KAAK,EAAE5G,KAAK,CAAC4E,MAAM,CAACE,QAAQ;IAC5BoE,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}