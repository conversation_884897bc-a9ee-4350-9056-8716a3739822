{"ast": null, "code": "export * from \"./StatusBar.types\";\nexport { default as setStatusBarBackgroundColor } from \"./setStatusBarBackgroundColor\";\nexport { default as setStatusBarNetworkActivityIndicatorVisible } from \"./setStatusBarNetworkActivityIndicatorVisible\";\nexport { default as setStatusBarHidden } from \"./setStatusBarHidden\";\nexport { default as setStatusBarStyle } from \"./setStatusBarStyle\";\nexport { default as setStatusBarTranslucent } from \"./setStatusBarTranslucent\";\nexport { default as StatusBar } from \"./ExpoStatusBar\";", "map": {"version": 3, "names": ["default", "setStatusBarBackgroundColor", "setStatusBarNetworkActivityIndicatorVisible", "setStatusBarHidden", "setStatusBarStyle", "setStatusBarTranslucent", "StatusBar"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\expo-status-bar\\src\\StatusBar.ts"], "sourcesContent": ["export * from './StatusBar.types';\nexport { default as setStatusBarBackgroundColor } from './setStatusBarBackgroundColor';\nexport { default as setStatusBarNetworkActivityIndicatorVisible } from './setStatusBarNetworkActivityIndicatorVisible';\nexport { default as setStatusBarHidden } from './setStatusBarHidden';\nexport { default as setStatusBarStyle } from './setStatusBarStyle';\nexport { default as setStatusBarTranslucent } from './setStatusBarTranslucent';\nexport { default as StatusBar } from './ExpoStatusBar';\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,2BAA2B;AAC/C,SAASD,OAAO,IAAIE,2CAA2C;AAC/D,SAASF,OAAO,IAAIG,kBAAkB;AACtC,SAASH,OAAO,IAAII,iBAAiB;AACrC,SAASJ,OAAO,IAAIK,uBAAuB;AAC3C,SAASL,OAAO,IAAIM,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}