<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerar APK - Bolão Cambista</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f8fafc; }
        .header { background: #1e40af; color: white; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px; }
        .method { background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .method h3 { color: #1e40af; margin-top: 0; }
        .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; margin: 10px 5px; }
        .button:hover { background: #047857; }
        .code { background: #f1f5f9; padding: 15px; border-radius: 8px; font-family: monospace; margin: 10px 0; overflow-x: auto; }
        .highlight { background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 Gerar APK - Bolão Cambista</h1>
        <p>Escolha o método mais fácil para gerar seu APK</p>
    </div>
    
    <div class="method">
        <h3>🚀 Método 1: Expo Snack (Mais Fácil)</h3>
        <p>Gere o APK online sem instalar nada:</p>
        <ol>
            <li>Acesse o Expo Snack</li>
            <li>Cole o código do app</li>
            <li>Clique em "Build APK"</li>
            <li>Baixe o APK pronto</li>
        </ol>
        <a href="https://snack.expo.dev/" target="_blank" class="button">🌐 Abrir Expo Snack</a>
        <a href="#codigo" class="button">📝 Ver Código</a>
    </div>
    
    <div class="method">
        <h3>📱 Método 2: Expo Go (Teste Imediato)</h3>
        <p>Teste o app agora mesmo no seu celular:</p>
        <ol>
            <li>Instale "Expo Go" no celular</li>
            <li>Execute o comando abaixo</li>
            <li>Escaneie o QR Code</li>
        </ol>
        <div class="code">npx expo start</div>
        <a href="https://play.google.com/store/apps/details?id=host.exp.exponent" target="_blank" class="button">📱 Baixar Expo Go</a>
    </div>
    
    <div class="method">
        <h3>🔥 Método 3: Appetize.io (Alternativo)</h3>
        <p>Outra opção para gerar APK online:</p>
        <a href="https://appetize.io/" target="_blank" class="button">🌐 Abrir Appetize.io</a>
    </div>
    
    <div class="highlight">
        <h4>🎯 Credenciais de Teste</h4>
        <p><strong>Email:</strong> <EMAIL></p>
        <p><strong>Senha:</strong> 1234</p>
        <p><em>Ou use qualquer email + senha com 4+ caracteres</em></p>
    </div>
    
    <div id="codigo" class="method">
        <h3>📝 Código do App.js</h3>
        <p>Cole este código no Expo Snack:</p>
        <textarea style="width: 100%; height: 200px; font-family: monospace; padding: 10px;" readonly>
import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';

export default function App() {
  const [email, setEmail] = useState('');
  const [senha, setSenha] = useState('');
  const [loggedIn, setLoggedIn] = useState(false);

  const handleLogin = () => {
    if (email && senha.length >= 4) {
      setLoggedIn(true);
      Alert.alert('Sucesso', 'Login realizado!');
    } else {
      Alert.alert('Erro', 'Email e senha (4+ chars) obrigatórios');
    }
  };

  if (loggedIn) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🎯 Bolão Cambista</Text>
          <Text style={styles.subtitle}>Bem-vindo!</Text>
        </View>
        
        <ScrollView style={styles.content}>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>📊 Dashboard</Text>
            <Text style={styles.stat}>👥 Usuários: 25</Text>
            <Text style={styles.stat}>🎫 Bilhetes: 12</Text>
            <Text style={styles.stat}>💰 Vendas: R$ 2.450</Text>
          </View>
          
          <TouchableOpacity style={styles.button} onPress={() => Alert.alert('Funcionalidade', 'Gerenciar Usuários')}>
            <Text style={styles.buttonText}>👥 Gerenciar Usuários</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={() => Alert.alert('Funcionalidade', 'Criar Bilhete')}>
            <Text style={styles.buttonText}>🎫 Criar Bilhete</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={() => Alert.alert('Funcionalidade', 'Ver Relatórios')}>
            <Text style={styles.buttonText}>📊 Relatórios</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.logoutButton]} onPress={() => setLoggedIn(false)}>
            <Text style={styles.buttonText}>🚪 Sair</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🎯 Bolão Cambista</Text>
        <Text style={styles.subtitle}>App para Cambistas</Text>
      </View>
      
      <View style={styles.loginForm}>
        <Text style={styles.loginTitle}>Entrar</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <TextInput
          style={styles.input}
          placeholder="Senha"
          value={senha}
          onChangeText={setSenha}
          secureTextEntry
        />
        
        <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
          <Text style={styles.buttonText}>Entrar</Text>
        </TouchableOpacity>
        
        <Text style={styles.demoText}>
          📱 DEMO: Use qualquer email e senha com 4+ caracteres
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8fafc' },
  header: { backgroundColor: '#1e40af', paddingTop: 50, paddingBottom: 30, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 5 },
  subtitle: { fontSize: 16, color: '#e2e8f0' },
  content: { flex: 1, padding: 20 },
  loginForm: { flex: 1, padding: 20, justifyContent: 'center' },
  loginTitle: { fontSize: 24, fontWeight: 'bold', color: '#1e40af', textAlign: 'center', marginBottom: 30 },
  input: { backgroundColor: '#fff', borderWidth: 1, borderColor: '#d1d5db', borderRadius: 8, padding: 15, marginBottom: 15, fontSize: 16 },
  loginButton: { backgroundColor: '#1e40af', borderRadius: 8, padding: 15, alignItems: 'center', marginTop: 10 },
  button: { backgroundColor: '#fff', borderRadius: 8, padding: 15, marginBottom: 10, alignItems: 'center', shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3 },
  logoutButton: { backgroundColor: '#dc2626', marginTop: 20 },
  buttonText: { fontSize: 16, color: '#1e40af', fontWeight: '500' },
  card: { backgroundColor: '#fff', borderRadius: 12, padding: 20, marginBottom: 20, shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3 },
  cardTitle: { fontSize: 18, fontWeight: 'bold', color: '#1e40af', marginBottom: 15 },
  stat: { fontSize: 16, color: '#374151', marginBottom: 8 },
  demoText: { textAlign: 'center', color: '#059669', fontSize: 14, fontWeight: 'bold', marginTop: 15 }
});
        </textarea>
        <button onclick="navigator.clipboard.writeText(this.previousElementSibling.value)" class="button">📋 Copiar Código</button>
    </div>
</body>
</html>