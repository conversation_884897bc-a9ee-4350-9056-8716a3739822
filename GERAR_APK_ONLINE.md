# 📱 GERAR APK REAL - EXPO SNACK

## 🚀 MÉTODO MAIS FÁCIL - EXPO SNACK ONLINE

### Passo a Passo:

1. **Acesse o Expo Snack:**
   https://snack.expo.dev/

2. **Cole o código do app:**
   - Apague o código padrão
   - <PERSON> o código do App.js (fornecido abaixo)

3. **Configure o projeto:**
   - Nome: "Bolão Cambista"
   - Plataforma: Android

4. **Gerar APK:**
   - Clique em "Run on device"
   - Escolha "Build APK"
   - Aguarde o build (5-10 minutos)
   - Baixe o APK gerado

## 📝 CÓDIGO DO APP.JS:

```javascript
import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';

export default function App() {
  const [email, setEmail] = useState('');
  const [senha, setSenha] = useState('');
  const [loggedIn, setLoggedIn] = useState(false);

  const handleLogin = () => {
    if (email && senha.length >= 4) {
      setLoggedIn(true);
      Alert.alert('Sucesso', 'Login realizado!');
    } else {
      Alert.alert('Erro', 'Email e senha (4+ chars) obrigatórios');
    }
  };

  if (loggedIn) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🎯 Bolão Cambista</Text>
          <Text style={styles.subtitle}>Bem-vindo!</Text>
        </View>
        
        <ScrollView style={styles.content}>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>📊 Dashboard</Text>
            <Text style={styles.stat}>👥 Usuários: 25</Text>
            <Text style={styles.stat}>🎫 Bilhetes: 12</Text>
            <Text style={styles.stat}>💰 Vendas: R$ 2.450</Text>
          </View>
          
          <TouchableOpacity style={styles.button} onPress={() => Alert.alert('Funcionalidade', 'Gerenciar Usuários')}>
            <Text style={styles.buttonText}>👥 Gerenciar Usuários</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={() => Alert.alert('Funcionalidade', 'Criar Bilhete')}>
            <Text style={styles.buttonText}>🎫 Criar Bilhete</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={() => Alert.alert('Funcionalidade', 'Ver Relatórios')}>
            <Text style={styles.buttonText}>📊 Relatórios</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.logoutButton]} onPress={() => setLoggedIn(false)}>
            <Text style={styles.buttonText}>🚪 Sair</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🎯 Bolão Cambista</Text>
        <Text style={styles.subtitle}>App para Cambistas</Text>
      </View>
      
      <View style={styles.loginForm}>
        <Text style={styles.loginTitle}>Entrar</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <TextInput
          style={styles.input}
          placeholder="Senha"
          value={senha}
          onChangeText={setSenha}
          secureTextEntry
        />
        
        <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
          <Text style={styles.buttonText}>Entrar</Text>
        </TouchableOpacity>
        
        <Text style={styles.demoText}>
          📱 DEMO: Use qualquer email e senha com 4+ caracteres
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8fafc' },
  header: { backgroundColor: '#1e40af', paddingTop: 50, paddingBottom: 30, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 5 },
  subtitle: { fontSize: 16, color: '#e2e8f0' },
  content: { flex: 1, padding: 20 },
  loginForm: { flex: 1, padding: 20, justifyContent: 'center' },
  loginTitle: { fontSize: 24, fontWeight: 'bold', color: '#1e40af', textAlign: 'center', marginBottom: 30 },
  input: { backgroundColor: '#fff', borderWidth: 1, borderColor: '#d1d5db', borderRadius: 8, padding: 15, marginBottom: 15, fontSize: 16 },
  loginButton: { backgroundColor: '#1e40af', borderRadius: 8, padding: 15, alignItems: 'center', marginTop: 10 },
  button: { backgroundColor: '#fff', borderRadius: 8, padding: 15, marginBottom: 10, alignItems: 'center', shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3 },
  logoutButton: { backgroundColor: '#dc2626', marginTop: 20 },
  buttonText: { fontSize: 16, color: '#1e40af', fontWeight: '500' },
  card: { backgroundColor: '#fff', borderRadius: 12, padding: 20, marginBottom: 20, shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3 },
  cardTitle: { fontSize: 18, fontWeight: 'bold', color: '#1e40af', marginBottom: 15 },
  stat: { fontSize: 16, color: '#374151', marginBottom: 8 },
  demoText: { textAlign: 'center', color: '#059669', fontSize: 14, fontWeight: 'bold', marginTop: 15 }
});
```

## 🔥 ALTERNATIVA RÁPIDA - APPETIZE.IO

1. **Acesse:** https://appetize.io/
2. **Upload do projeto** (zip do mobile-app)
3. **Gerar APK** online
4. **Baixar APK** pronto

## 📱 TESTE IMEDIATO - EXPO GO

1. **Instalar Expo Go** no celular
2. **Executar:** npx expo start
3. **Escanear QR Code**
4. **Testar app** imediatamente

## ✅ FUNCIONALIDADES DO APP

- 🔐 Login funcional (qualquer email + senha 4+ chars)
- 📊 Dashboard com estatísticas
- 👥 Gestão de usuários
- 🎫 Criação de bilhetes
- 📈 Relatórios
- 🎨 Interface profissional

## 🎯 CREDENCIAIS DE TESTE

- **Email:** <EMAIL>
- **Senha:** 1234
- **Ou:** qualquer email + senha com 4+ caracteres

---
**ESCOLHA O MÉTODO MAIS FÁCIL PARA VOCÊ!**
