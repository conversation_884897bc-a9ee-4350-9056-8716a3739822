import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  StatusBar,
  Modal,
  FlatList,
  RefreshControl,
} from 'react-native';

export default function App() {
  const [email, setEmail] = useState('');
  const [senha, setSenha] = useState('');
  const [loggedIn, setLoggedIn] = useState(false);
  const [currentScreen, setCurrentScreen] = useState('home');
  const [userInfo, setUserInfo] = useState(null);
  const [usuarios, setUsuarios] = useState([]);
  const [bilhetes, setBilhetes] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [novoUsuario, setNovoUsuario] = useState({ nome: '', email: '', saldo: '' });

  // Dados simulados do sistema
  const [stats, setStats] = useState({
    totalUsuarios: 125,
    bilhetesHoje: 47,
    vendasHoje: 8750.50,
    comissaoHoje: 875.05,
    usuariosAtivos: 89,
    bilhetesTotal: 1247
  });

  useEffect(() => {
    // Simular dados iniciais
    setUsuarios([
      { id: 1, nome: 'João Silva', email: '<EMAIL>', saldo: 150.00, ativo: true },
      { id: 2, nome: 'Maria Santos', email: '<EMAIL>', saldo: 250.75, ativo: true },
      { id: 3, nome: 'Pedro Costa', email: '<EMAIL>', saldo: 89.30, ativo: false },
      { id: 4, nome: 'Ana Oliveira', email: '<EMAIL>', saldo: 320.00, ativo: true },
      { id: 5, nome: 'Carlos Lima', email: '<EMAIL>', saldo: 45.50, ativo: true },
    ]);

    setBilhetes([
      { id: 1, usuario: 'João Silva', numeros: '12-25-33-41-58', valor: 25.00, data: '29/07/2025', status: 'Ativo' },
      { id: 2, usuario: 'Maria Santos', numeros: '07-14-28-35-49', valor: 50.00, data: '29/07/2025', status: 'Ativo' },
      { id: 3, usuario: 'Ana Oliveira', numeros: '03-19-27-44-52', valor: 15.00, data: '28/07/2025', status: 'Premiado' },
      { id: 4, usuario: 'Carlos Lima', numeros: '08-16-23-39-56', valor: 30.00, data: '28/07/2025', status: 'Ativo' },
    ]);
  }, []);

  const handleLogin = async () => {
    if (!email || !senha) {
      Alert.alert('Erro', 'Preencha todos os campos');
      return;
    }

    // Simular autenticação
    if (email.includes('@') && senha.length >= 4) {
      setUserInfo({
        nome: 'Cambista Sistema',
        email: email,
        tipo: 'cambista',
        id: 1
      });
      setLoggedIn(true);
      Alert.alert('Sucesso', 'Login realizado com sucesso!');
    } else {
      Alert.alert('Erro', 'Credenciais inválidas');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Sair',
      'Deseja realmente sair do sistema?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Sair', 
          onPress: () => {
            setLoggedIn(false);
            setUserInfo(null);
            setEmail('');
            setSenha('');
            setCurrentScreen('home');
          }
        }
      ]
    );
  };

  const onRefresh = () => {
    setRefreshing(true);
    // Simular atualização de dados
    setTimeout(() => {
      setStats(prev => ({
        ...prev,
        bilhetesHoje: prev.bilhetesHoje + Math.floor(Math.random() * 5),
        vendasHoje: prev.vendasHoje + (Math.random() * 500),
      }));
      setRefreshing(false);
    }, 1000);
  };

  const adicionarUsuario = () => {
    if (!novoUsuario.nome || !novoUsuario.email) {
      Alert.alert('Erro', 'Preencha nome e email');
      return;
    }

    const usuario = {
      id: usuarios.length + 1,
      nome: novoUsuario.nome,
      email: novoUsuario.email,
      saldo: parseFloat(novoUsuario.saldo) || 0,
      ativo: true
    };

    setUsuarios([...usuarios, usuario]);
    setNovoUsuario({ nome: '', email: '', saldo: '' });
    setModalVisible(false);
    Alert.alert('Sucesso', 'Usuário adicionado com sucesso!');
  };

  const alterarSaldo = (userId, novoSaldo) => {
    Alert.prompt(
      'Alterar Saldo',
      'Digite o novo saldo:',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Confirmar',
          onPress: (valor) => {
            const saldo = parseFloat(valor);
            if (!isNaN(saldo)) {
              setUsuarios(usuarios.map(u => 
                u.id === userId ? { ...u, saldo } : u
              ));
              Alert.alert('Sucesso', 'Saldo atualizado!');
            }
          }
        }
      ],
      'plain-text',
      novoSaldo.toString()
    );
  };

  const criarBilhete = () => {
    const numeros = Array.from({length: 5}, () => 
      Math.floor(Math.random() * 60) + 1
    ).sort((a, b) => a - b).join('-');

    const novoBilhete = {
      id: bilhetes.length + 1,
      usuario: 'Sistema',
      numeros: numeros.padStart(2, '0'),
      valor: 25.00,
      data: new Date().toLocaleDateString('pt-BR'),
      status: 'Ativo'
    };

    setBilhetes([novoBilhete, ...bilhetes]);
    Alert.alert('Sucesso', `Bilhete criado!\nNúmeros: ${numeros}`);
  };

  if (!loggedIn) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#1e40af" />
        
        <View style={styles.header}>
          <Text style={styles.headerTitle}>🎯 Sistema Bolão</Text>
          <Text style={styles.headerSubtitle}>Cambista Mobile</Text>
        </View>

        <View style={styles.loginContainer}>
          <Text style={styles.loginTitle}>Entrar no Sistema</Text>
          
          <TextInput
            style={styles.input}
            placeholder="Email do cambista"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Senha"
            value={senha}
            onChangeText={setSenha}
            secureTextEntry
          />
          
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginButtonText}>Entrar</Text>
          </TouchableOpacity>
          
          <View style={styles.demoInfo}>
            <Text style={styles.demoTitle}>📱 DEMO - Sistema Completo</Text>
            <Text style={styles.demoText}>Email: <EMAIL></Text>
            <Text style={styles.demoText}>Senha: 1234</Text>
            <Text style={styles.infoText}>
              Sistema completo com gestão de usuários, bilhetes, relatórios e muito mais!
            </Text>
          </View>
        </View>
      </View>
    );
  }

  const renderHome = () => (
    <ScrollView 
      style={styles.content}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
    >
      <Text style={styles.welcomeText}>Bem-vindo, {userInfo?.nome}!</Text>
      
      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalUsuarios}</Text>
          <Text style={styles.statLabel}>Total Usuários</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.bilhetesHoje}</Text>
          <Text style={styles.statLabel}>Bilhetes Hoje</Text>
        </View>
      </View>

      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>R$ {stats.vendasHoje.toFixed(2)}</Text>
          <Text style={styles.statLabel}>Vendas Hoje</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>R$ {stats.comissaoHoje.toFixed(2)}</Text>
          <Text style={styles.statLabel}>Comissão</Text>
        </View>
      </View>

      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={() => setCurrentScreen('usuarios')}>
          <Text style={styles.actionButtonText}>👥 Gerenciar Usuários</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={() => setCurrentScreen('bilhetes')}>
          <Text style={styles.actionButtonText}>🎫 Bilhetes</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={() => setCurrentScreen('relatorios')}>
          <Text style={styles.actionButtonText}>📊 Relatórios</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={criarBilhete}>
          <Text style={styles.actionButtonText}>🎲 Criar Bilhete</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderUsuarios = () => (
    <ScrollView style={styles.content}>
      <View style={styles.screenHeader}>
        <Text style={styles.screenTitle}>👥 Usuários</Text>
        <TouchableOpacity style={styles.addButton} onPress={() => setModalVisible(true)}>
          <Text style={styles.addButtonText}>+ Adicionar</Text>
        </TouchableOpacity>
      </View>

      {usuarios.map(usuario => (
        <View key={usuario.id} style={styles.userCard}>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{usuario.nome}</Text>
            <Text style={styles.userEmail}>{usuario.email}</Text>
            <Text style={[styles.userStatus, { color: usuario.ativo ? '#059669' : '#dc2626' }]}>
              {usuario.ativo ? '● Ativo' : '● Inativo'}
            </Text>
          </View>
          <View style={styles.userActions}>
            <Text style={styles.userSaldo}>R$ {usuario.saldo.toFixed(2)}</Text>
            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => alterarSaldo(usuario.id, usuario.saldo)}
            >
              <Text style={styles.editButtonText}>Editar</Text>
            </TouchableOpacity>
          </View>
        </View>
      ))}
    </ScrollView>
  );

  const renderBilhetes = () => (
    <ScrollView style={styles.content}>
      <Text style={styles.screenTitle}>🎫 Bilhetes</Text>
      
      {bilhetes.map(bilhete => (
        <View key={bilhete.id} style={styles.ticketCard}>
          <View style={styles.ticketHeader}>
            <Text style={styles.ticketUser}>{bilhete.usuario}</Text>
            <Text style={[styles.ticketStatus, { 
              color: bilhete.status === 'Premiado' ? '#059669' : '#1e40af' 
            }]}>
              {bilhete.status}
            </Text>
          </View>
          <Text style={styles.ticketNumbers}>{bilhete.numeros}</Text>
          <View style={styles.ticketFooter}>
            <Text style={styles.ticketValue}>R$ {bilhete.valor.toFixed(2)}</Text>
            <Text style={styles.ticketDate}>{bilhete.data}</Text>
          </View>
        </View>
      ))}
    </ScrollView>
  );

  const renderRelatorios = () => (
    <ScrollView style={styles.content}>
      <Text style={styles.screenTitle}>📊 Relatórios</Text>
      
      <View style={styles.reportCard}>
        <Text style={styles.reportTitle}>Resumo Geral</Text>
        <Text style={styles.reportItem}>👥 Usuários Ativos: {stats.usuariosAtivos}</Text>
        <Text style={styles.reportItem}>🎫 Total de Bilhetes: {stats.bilhetesTotal}</Text>
        <Text style={styles.reportItem}>💰 Vendas do Mês: R$ 45.750,00</Text>
        <Text style={styles.reportItem}>📈 Comissão do Mês: R$ 4.575,00</Text>
      </View>

      <View style={styles.reportCard}>
        <Text style={styles.reportTitle}>Performance Hoje</Text>
        <Text style={styles.reportItem}>🎯 Meta Diária: R$ 10.000,00</Text>
        <Text style={styles.reportItem}>✅ Realizado: R$ {stats.vendasHoje.toFixed(2)}</Text>
        <Text style={styles.reportItem}>📊 Progresso: {((stats.vendasHoje / 10000) * 100).toFixed(1)}%</Text>
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1e40af" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🎯 Sistema Bolão</Text>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutBtn}>
          <Text style={styles.logoutText}>Sair</Text>
        </TouchableOpacity>
      </View>

      {currentScreen === 'home' && renderHome()}
      {currentScreen === 'usuarios' && renderUsuarios()}
      {currentScreen === 'bilhetes' && renderBilhetes()}
      {currentScreen === 'relatorios' && renderRelatorios()}

      <View style={styles.bottomNav}>
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'home' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('home')}
        >
          <Text style={[styles.navText, currentScreen === 'home' && styles.navTextActive]}>🏠 Home</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'usuarios' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('usuarios')}
        >
          <Text style={[styles.navText, currentScreen === 'usuarios' && styles.navTextActive]}>👥 Usuários</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'bilhetes' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('bilhetes')}
        >
          <Text style={[styles.navText, currentScreen === 'bilhetes' && styles.navTextActive]}>🎫 Bilhetes</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.navButton, currentScreen === 'relatorios' && styles.navButtonActive]}
          onPress={() => setCurrentScreen('relatorios')}
        >
          <Text style={[styles.navText, currentScreen === 'relatorios' && styles.navTextActive]}>📊 Relatórios</Text>
        </TouchableOpacity>
      </View>

      {/* Modal para adicionar usuário */}
      <Modal visible={modalVisible} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Adicionar Usuário</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Nome completo"
              value={novoUsuario.nome}
              onChangeText={(text) => setNovoUsuario({...novoUsuario, nome: text})}
            />
            
            <TextInput
              style={styles.input}
              placeholder="Email"
              value={novoUsuario.email}
              onChangeText={(text) => setNovoUsuario({...novoUsuario, email: text})}
              keyboardType="email-address"
            />
            
            <TextInput
              style={styles.input}
              placeholder="Saldo inicial (opcional)"
              value={novoUsuario.saldo}
              onChangeText={(text) => setNovoUsuario({...novoUsuario, saldo: text})}
              keyboardType="numeric"
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]} 
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.modalButton, styles.confirmButton]} 
                onPress={adicionarUsuario}
              >
                <Text style={styles.confirmButtonText}>Adicionar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8fafc' },
  header: { 
    backgroundColor: '#1e40af', 
    paddingTop: 50, 
    paddingBottom: 20, 
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  headerTitle: { fontSize: 24, fontWeight: 'bold', color: '#fff' },
  headerSubtitle: { fontSize: 16, color: '#e2e8f0' },
  logoutBtn: { padding: 8 },
  logoutText: { color: '#fff', fontSize: 14 },
  content: { flex: 1, padding: 20 },
  loginContainer: { flex: 1, padding: 20, justifyContent: 'center' },
  loginTitle: { fontSize: 28, fontWeight: 'bold', color: '#1e40af', textAlign: 'center', marginBottom: 40 },
  input: { 
    backgroundColor: '#fff', 
    borderWidth: 1, 
    borderColor: '#d1d5db', 
    borderRadius: 12, 
    padding: 16, 
    marginBottom: 16, 
    fontSize: 16 
  },
  loginButton: { 
    backgroundColor: '#1e40af', 
    borderRadius: 12, 
    padding: 18, 
    alignItems: 'center', 
    marginTop: 10 
  },
  loginButtonText: { color: '#fff', fontSize: 18, fontWeight: 'bold' },
  demoInfo: { 
    backgroundColor: '#ecfdf5', 
    padding: 20, 
    borderRadius: 12, 
    marginTop: 30,
    borderWidth: 1,
    borderColor: '#059669'
  },
  demoTitle: { fontSize: 16, fontWeight: 'bold', color: '#059669', marginBottom: 10 },
  demoText: { fontSize: 14, color: '#065f46', marginBottom: 5 },
  infoText: { fontSize: 12, color: '#6b7280', marginTop: 10, textAlign: 'center' },
  welcomeText: { fontSize: 20, fontWeight: 'bold', color: '#1e40af', marginBottom: 20 },
  statsGrid: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 15 },
  statCard: { 
    backgroundColor: '#1e40af', 
    borderRadius: 16, 
    padding: 20, 
    flex: 1, 
    marginHorizontal: 5, 
    alignItems: 'center' 
  },
  statNumber: { fontSize: 24, fontWeight: 'bold', color: '#fff', marginBottom: 5 },
  statLabel: { fontSize: 12, color: '#e2e8f0', textAlign: 'center' },
  actionsContainer: { marginTop: 20 },
  actionButton: { 
    backgroundColor: '#fff', 
    borderRadius: 12, 
    padding: 18, 
    marginBottom: 12, 
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  actionButtonText: { fontSize: 16, color: '#1e40af', fontWeight: '600' },
  bottomNav: { 
    flexDirection: 'row', 
    backgroundColor: '#fff', 
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb'
  },
  navButton: { flex: 1, alignItems: 'center', paddingVertical: 8 },
  navButtonActive: { backgroundColor: '#eff6ff' },
  navText: { fontSize: 12, color: '#6b7280' },
  navTextActive: { color: '#1e40af', fontWeight: 'bold' },
  screenHeader: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    alignItems: 'center', 
    marginBottom: 20 
  },
  screenTitle: { fontSize: 24, fontWeight: 'bold', color: '#1e40af' },
  addButton: { backgroundColor: '#059669', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 8 },
  addButtonText: { color: '#fff', fontWeight: 'bold' },
  userCard: { 
    backgroundColor: '#fff', 
    borderRadius: 12, 
    padding: 16, 
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  userInfo: { flex: 1 },
  userName: { fontSize: 16, fontWeight: 'bold', color: '#1f2937' },
  userEmail: { fontSize: 14, color: '#6b7280', marginTop: 2 },
  userStatus: { fontSize: 12, marginTop: 4 },
  userActions: { alignItems: 'flex-end' },
  userSaldo: { fontSize: 16, fontWeight: 'bold', color: '#059669', marginBottom: 8 },
  editButton: { backgroundColor: '#1e40af', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 6 },
  editButtonText: { color: '#fff', fontSize: 12 },
  ticketCard: { 
    backgroundColor: '#fff', 
    borderRadius: 12, 
    padding: 16, 
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  ticketHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  ticketUser: { fontSize: 16, fontWeight: 'bold', color: '#1f2937' },
  ticketStatus: { fontSize: 14, fontWeight: 'bold' },
  ticketNumbers: { 
    fontSize: 20, 
    fontWeight: 'bold', 
    color: '#1e40af', 
    textAlign: 'center', 
    marginVertical: 12,
    letterSpacing: 2
  },
  ticketFooter: { flexDirection: 'row', justifyContent: 'space-between' },
  ticketValue: { fontSize: 16, fontWeight: 'bold', color: '#059669' },
  ticketDate: { fontSize: 14, color: '#6b7280' },
  reportCard: { 
    backgroundColor: '#fff', 
    borderRadius: 12, 
    padding: 20, 
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  reportTitle: { fontSize: 18, fontWeight: 'bold', color: '#1e40af', marginBottom: 12 },
  reportItem: { fontSize: 14, color: '#374151', marginBottom: 8 },
  modalOverlay: { 
    flex: 1, 
    backgroundColor: 'rgba(0,0,0,0.5)', 
    justifyContent: 'center', 
    alignItems: 'center' 
  },
  modalContent: { 
    backgroundColor: '#fff', 
    borderRadius: 16, 
    padding: 24, 
    width: '90%', 
    maxWidth: 400 
  },
  modalTitle: { fontSize: 20, fontWeight: 'bold', color: '#1e40af', marginBottom: 20, textAlign: 'center' },
  modalButtons: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 20 },
  modalButton: { flex: 1, padding: 12, borderRadius: 8, marginHorizontal: 5 },
  cancelButton: { backgroundColor: '#f3f4f6' },
  confirmButton: { backgroundColor: '#1e40af' },
  cancelButtonText: { color: '#374151', textAlign: 'center', fontWeight: 'bold' },
  confirmButtonText: { color: '#fff', textAlign: 'center', fontWeight: 'bold' },
});
