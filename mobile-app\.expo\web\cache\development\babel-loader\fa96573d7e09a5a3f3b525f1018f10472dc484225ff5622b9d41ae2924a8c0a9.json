{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nimport canUseDOM from \"../canUseDom\";\nvar useLayoutEffectImpl = canUseDOM ? useLayoutEffect : useEffect;\nexport default useLayoutEffectImpl;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "canUseDOM", "useLayoutEffectImpl"], "sources": ["C:/laragon/www/mobile-app/node_modules/react-native-web/dist/modules/useLayoutEffect/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * useLayoutEffect throws an error on the server. On the few occasions where is\n * problematic, use this hook.\n *\n * \n */\n\nimport { useEffect, useLayoutEffect } from 'react';\nimport canUseDOM from '../canUseDom';\nvar useLayoutEffectImpl = canUseDOM ? useLayoutEffect : useEffect;\nexport default useLayoutEffectImpl;"], "mappings": "AAYA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,OAAOC,SAAS;AAChB,IAAIC,mBAAmB,GAAGD,SAAS,GAAGD,eAAe,GAAGD,SAAS;AACjE,eAAeG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}