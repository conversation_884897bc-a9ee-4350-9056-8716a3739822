{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useEffect, useState } from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { Provider as PaperProvider } from 'react-native-paper';\nimport { StatusBar } from 'expo-status-bar';\nimport * as SecureStore from 'expo-secure-store';\nimport { Ionicons } from '@expo/vector-icons';\nimport LoginScreen from \"./src/screens/LoginScreen\";\nimport HomeScreen from \"./src/screens/HomeScreen\";\nimport UsersScreen from \"./src/screens/UsersScreen\";\nimport BilhetesScreen from \"./src/screens/BilhetesScreen\";\nimport RelatoriosScreen from \"./src/screens/RelatoriosScreen\";\nimport PerfilScreen from \"./src/screens/PerfilScreen\";\nimport CriarBilheteScreen from \"./src/screens/CriarBilheteScreen\";\nimport QRCodeScannerScreen from \"./src/screens/QRCodeScannerScreen\";\nimport { theme } from \"./src/theme/theme\";\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nvar Stack = createNativeStackNavigator();\nvar Tab = createBottomTabNavigator();\nfunction MainTabs() {\n  return _jsxs(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref) {\n      var route = _ref.route;\n      return {\n        tabBarIcon: function tabBarIcon(_ref2) {\n          var focused = _ref2.focused,\n            color = _ref2.color,\n            size = _ref2.size;\n          var iconName;\n          if (route.name === 'Home') {\n            iconName = focused ? 'home' : 'home-outline';\n          } else if (route.name === 'Usuários') {\n            iconName = focused ? 'people' : 'people-outline';\n          } else if (route.name === 'Bilhetes') {\n            iconName = focused ? 'receipt' : 'receipt-outline';\n          } else if (route.name === 'Relatórios') {\n            iconName = focused ? 'bar-chart' : 'bar-chart-outline';\n          } else if (route.name === 'Perfil') {\n            iconName = focused ? 'person' : 'person-outline';\n          } else {\n            iconName = 'help-outline';\n          }\n          return _jsx(Ionicons, {\n            name: iconName,\n            size: size,\n            color: color\n          });\n        },\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: 'gray',\n        headerStyle: {\n          backgroundColor: theme.colors.primary\n        },\n        headerTintColor: '#fff',\n        headerTitleStyle: {\n          fontWeight: 'bold'\n        }\n      };\n    },\n    children: [_jsx(Tab.Screen, {\n      name: \"Home\",\n      component: HomeScreen\n    }), _jsx(Tab.Screen, {\n      name: \"Usu\\xE1rios\",\n      component: UsersScreen\n    }), _jsx(Tab.Screen, {\n      name: \"Bilhetes\",\n      component: BilhetesScreen\n    }), _jsx(Tab.Screen, {\n      name: \"Relat\\xF3rios\",\n      component: RelatoriosScreen\n    }), _jsx(Tab.Screen, {\n      name: \"Perfil\",\n      component: PerfilScreen\n    })]\n  });\n}\nexport default function App() {\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    isAuthenticated = _useState2[0],\n    setIsAuthenticated = _useState2[1];\n  useEffect(function () {\n    checkAuthStatus();\n  }, []);\n  var checkAuthStatus = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      try {\n        var token = yield SecureStore.getItemAsync('userToken');\n        var userData = yield SecureStore.getItemAsync('userData');\n        if (token && userData) {\n          var user = JSON.parse(userData);\n          if (user.tipo === 'cambista') {\n            setIsAuthenticated(true);\n          } else {\n            setIsAuthenticated(false);\n          }\n        } else {\n          setIsAuthenticated(false);\n        }\n      } catch (error) {\n        console.error('Erro ao verificar autenticação:', error);\n        setIsAuthenticated(false);\n      }\n    });\n    return function checkAuthStatus() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  if (isAuthenticated === null) {\n    return null;\n  }\n  return _jsx(PaperProvider, {\n    theme: theme,\n    children: _jsxs(NavigationContainer, {\n      children: [_jsx(StatusBar, {\n        style: \"light\",\n        backgroundColor: theme.colors.primary\n      }), _jsx(Stack.Navigator, {\n        screenOptions: {\n          headerShown: false\n        },\n        children: !isAuthenticated ? _jsx(Stack.Screen, {\n          name: \"Login\",\n          children: function children(props) {\n            return _jsx(LoginScreen, _objectSpread(_objectSpread({}, props), {}, {\n              onLogin: function onLogin() {\n                return setIsAuthenticated(true);\n              }\n            }));\n          }\n        }) : _jsxs(_Fragment, {\n          children: [_jsx(Stack.Screen, {\n            name: \"MainTabs\",\n            component: MainTabs\n          }), _jsx(Stack.Screen, {\n            name: \"CriarBilhete\",\n            component: CriarBilheteScreen,\n            options: {\n              headerShown: true,\n              title: 'Criar Bilhete',\n              headerStyle: {\n                backgroundColor: theme.colors.primary\n              },\n              headerTintColor: '#fff'\n            }\n          }), _jsx(Stack.Screen, {\n            name: \"QRScanner\",\n            component: QRCodeScannerScreen,\n            options: {\n              headerShown: true,\n              title: 'Escanear QR Code',\n              headerStyle: {\n                backgroundColor: theme.colors.primary\n              },\n              headerTintColor: '#fff'\n            }\n          })]\n        })\n      })]\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "NavigationContainer", "createNativeStackNavigator", "createBottomTabNavigator", "Provider", "PaperProvider", "StatusBar", "SecureStore", "Ionicons", "LoginScreen", "HomeScreen", "UsersScreen", "BilhetesScreen", "RelatoriosScreen", "PerfilScreen", "CriarBilheteScreen", "QRCodeScannerScreen", "theme", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON>", "Tab", "MainTabs", "Navigator", "screenOptions", "_ref", "route", "tabBarIcon", "_ref2", "focused", "color", "size", "iconName", "name", "tabBarActiveTintColor", "colors", "primary", "tabBarInactiveTintColor", "headerStyle", "backgroundColor", "headerTintColor", "headerTitleStyle", "fontWeight", "children", "Screen", "component", "App", "_useState", "_useState2", "_slicedToArray", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "_ref3", "_asyncToGenerator", "token", "getItemAsync", "userData", "user", "JSON", "parse", "tipo", "error", "console", "apply", "arguments", "style", "headerShown", "props", "_objectSpread", "onLogin", "options", "title"], "sources": ["C:/laragon/www/mobile-app/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { Provider as PaperProvider } from 'react-native-paper';\nimport { StatusBar } from 'expo-status-bar';\nimport * as SecureStore from 'expo-secure-store';\nimport { Ionicons } from '@expo/vector-icons';\n\n// Screens\nimport LoginScreen from './src/screens/LoginScreen';\nimport HomeScreen from './src/screens/HomeScreen';\nimport UsersScreen from './src/screens/UsersScreen';\nimport BilhetesScreen from './src/screens/BilhetesScreen';\nimport RelatoriosScreen from './src/screens/RelatoriosScreen';\nimport PerfilScreen from './src/screens/PerfilScreen';\nimport CriarBilheteScreen from './src/screens/CriarBilheteScreen';\nimport QRCodeScannerScreen from './src/screens/QRCodeScannerScreen';\n\n// Theme\nimport { theme } from './src/theme/theme';\n\nconst Stack = createNativeStackNavigator();\nconst Tab = createBottomTabNavigator();\n\nfunction MainTabs() {\n  return (\n    <Tab.Navigator\n      screenOptions={({ route }) => ({\n        tabBarIcon: ({ focused, color, size }) => {\n          let iconName: keyof typeof Ionicons.glyphMap;\n\n          if (route.name === 'Home') {\n            iconName = focused ? 'home' : 'home-outline';\n          } else if (route.name === 'Usuários') {\n            iconName = focused ? 'people' : 'people-outline';\n          } else if (route.name === 'Bilhetes') {\n            iconName = focused ? 'receipt' : 'receipt-outline';\n          } else if (route.name === 'Relatórios') {\n            iconName = focused ? 'bar-chart' : 'bar-chart-outline';\n          } else if (route.name === 'Perfil') {\n            iconName = focused ? 'person' : 'person-outline';\n          } else {\n            iconName = 'help-outline';\n          }\n\n          return <Ionicons name={iconName} size={size} color={color} />;\n        },\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: 'gray',\n        headerStyle: {\n          backgroundColor: theme.colors.primary,\n        },\n        headerTintColor: '#fff',\n        headerTitleStyle: {\n          fontWeight: 'bold',\n        },\n      })}\n    >\n      <Tab.Screen name=\"Home\" component={HomeScreen} />\n      <Tab.Screen name=\"Usuários\" component={UsersScreen} />\n      <Tab.Screen name=\"Bilhetes\" component={BilhetesScreen} />\n      <Tab.Screen name=\"Relatórios\" component={RelatoriosScreen} />\n      <Tab.Screen name=\"Perfil\" component={PerfilScreen} />\n    </Tab.Navigator>\n  );\n}\n\nexport default function App() {\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      const token = await SecureStore.getItemAsync('userToken');\n      const userData = await SecureStore.getItemAsync('userData');\n      \n      if (token && userData) {\n        const user = JSON.parse(userData);\n        if (user.tipo === 'cambista') {\n          setIsAuthenticated(true);\n        } else {\n          setIsAuthenticated(false);\n        }\n      } else {\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.error('Erro ao verificar autenticação:', error);\n      setIsAuthenticated(false);\n    }\n  };\n\n  if (isAuthenticated === null) {\n    return null; // Loading screen could be added here\n  }\n\n  return (\n    <PaperProvider theme={theme}>\n      <NavigationContainer>\n        <StatusBar style=\"light\" backgroundColor={theme.colors.primary} />\n        <Stack.Navigator screenOptions={{ headerShown: false }}>\n          {!isAuthenticated ? (\n            <Stack.Screen name=\"Login\">\n              {(props) => <LoginScreen {...props} onLogin={() => setIsAuthenticated(true)} />}\n            </Stack.Screen>\n          ) : (\n            <>\n              <Stack.Screen name=\"MainTabs\" component={MainTabs} />\n              <Stack.Screen \n                name=\"CriarBilhete\" \n                component={CriarBilheteScreen}\n                options={{\n                  headerShown: true,\n                  title: 'Criar Bilhete',\n                  headerStyle: { backgroundColor: theme.colors.primary },\n                  headerTintColor: '#fff',\n                }}\n              />\n              <Stack.Screen \n                name=\"QRScanner\" \n                component={QRCodeScannerScreen}\n                options={{\n                  headerShown: true,\n                  title: 'Escanear QR Code',\n                  headerStyle: { backgroundColor: theme.colors.primary },\n                  headerTintColor: '#fff',\n                }}\n              />\n            </>\n          )}\n        </Stack.Navigator>\n      </NavigationContainer>\n    </PaperProvider>\n  );\n}\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,QAAQ,IAAIC,aAAa,QAAQ,oBAAoB;AAC9D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,oBAAoB;AAG7C,OAAOC,WAAW;AAClB,OAAOC,UAAU;AACjB,OAAOC,WAAW;AAClB,OAAOC,cAAc;AACrB,OAAOC,gBAAgB;AACvB,OAAOC,YAAY;AACnB,OAAOC,kBAAkB;AACzB,OAAOC,mBAAmB;AAG1B,SAASC,KAAK;AAA4B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,IAAMC,KAAK,GAAGtB,0BAA0B,CAAC,CAAC;AAC1C,IAAMuB,GAAG,GAAGtB,wBAAwB,CAAC,CAAC;AAEtC,SAASuB,QAAQA,CAAA,EAAG;EAClB,OACEL,KAAA,CAACI,GAAG,CAACE,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,IAAA;MAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAA,OAAQ;QAC7BC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAgC;UAAA,IAA3BC,OAAO,GAAAD,KAAA,CAAPC,OAAO;YAAEC,KAAK,GAAAF,KAAA,CAALE,KAAK;YAAEC,IAAI,GAAAH,KAAA,CAAJG,IAAI;UACjC,IAAIC,QAAwC;UAE5C,IAAIN,KAAK,CAACO,IAAI,KAAK,MAAM,EAAE;YACzBD,QAAQ,GAAGH,OAAO,GAAG,MAAM,GAAG,cAAc;UAC9C,CAAC,MAAM,IAAIH,KAAK,CAACO,IAAI,KAAK,UAAU,EAAE;YACpCD,QAAQ,GAAGH,OAAO,GAAG,QAAQ,GAAG,gBAAgB;UAClD,CAAC,MAAM,IAAIH,KAAK,CAACO,IAAI,KAAK,UAAU,EAAE;YACpCD,QAAQ,GAAGH,OAAO,GAAG,SAAS,GAAG,iBAAiB;UACpD,CAAC,MAAM,IAAIH,KAAK,CAACO,IAAI,KAAK,YAAY,EAAE;YACtCD,QAAQ,GAAGH,OAAO,GAAG,WAAW,GAAG,mBAAmB;UACxD,CAAC,MAAM,IAAIH,KAAK,CAACO,IAAI,KAAK,QAAQ,EAAE;YAClCD,QAAQ,GAAGH,OAAO,GAAG,QAAQ,GAAG,gBAAgB;UAClD,CAAC,MAAM;YACLG,QAAQ,GAAG,cAAc;UAC3B;UAEA,OAAOjB,IAAA,CAACX,QAAQ;YAAC6B,IAAI,EAAED,QAAS;YAACD,IAAI,EAAEA,IAAK;YAACD,KAAK,EAAEA;UAAM,CAAE,CAAC;QAC/D,CAAC;QACDI,qBAAqB,EAAErB,KAAK,CAACsB,MAAM,CAACC,OAAO;QAC3CC,uBAAuB,EAAE,MAAM;QAC/BC,WAAW,EAAE;UACXC,eAAe,EAAE1B,KAAK,CAACsB,MAAM,CAACC;QAChC,CAAC;QACDI,eAAe,EAAE,MAAM;QACvBC,gBAAgB,EAAE;UAChBC,UAAU,EAAE;QACd;MACF,CAAC;IAAA,CAAE;IAAAC,QAAA,GAEH5B,IAAA,CAACM,GAAG,CAACuB,MAAM;MAACX,IAAI,EAAC,MAAM;MAACY,SAAS,EAAEvC;IAAW,CAAE,CAAC,EACjDS,IAAA,CAACM,GAAG,CAACuB,MAAM;MAACX,IAAI,EAAC,aAAU;MAACY,SAAS,EAAEtC;IAAY,CAAE,CAAC,EACtDQ,IAAA,CAACM,GAAG,CAACuB,MAAM;MAACX,IAAI,EAAC,UAAU;MAACY,SAAS,EAAErC;IAAe,CAAE,CAAC,EACzDO,IAAA,CAACM,GAAG,CAACuB,MAAM;MAACX,IAAI,EAAC,eAAY;MAACY,SAAS,EAAEpC;IAAiB,CAAE,CAAC,EAC7DM,IAAA,CAACM,GAAG,CAACuB,MAAM;MAACX,IAAI,EAAC,QAAQ;MAACY,SAAS,EAAEnC;IAAa,CAAE,CAAC;EAAA,CACxC,CAAC;AAEpB;AAEA,eAAe,SAASoC,GAAGA,CAAA,EAAG;EAC5B,IAAAC,SAAA,GAA8CnD,QAAQ,CAAiB,IAAI,CAAC;IAAAoD,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAArEG,eAAe,GAAAF,UAAA;IAAEG,kBAAkB,GAAAH,UAAA;EAE1CrD,SAAS,CAAC,YAAM;IACdyD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,eAAe;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAClC,IAAI;QACF,IAAMC,KAAK,SAASpD,WAAW,CAACqD,YAAY,CAAC,WAAW,CAAC;QACzD,IAAMC,QAAQ,SAAStD,WAAW,CAACqD,YAAY,CAAC,UAAU,CAAC;QAE3D,IAAID,KAAK,IAAIE,QAAQ,EAAE;UACrB,IAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACjC,IAAIC,IAAI,CAACG,IAAI,KAAK,UAAU,EAAE;YAC5BV,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM;YACLA,kBAAkB,CAAC,KAAK,CAAC;UAC3B;QACF,CAAC,MAAM;UACLA,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDX,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC;IAAA,gBAnBKC,eAAeA,CAAA;MAAA,OAAAC,KAAA,CAAAW,KAAA,OAAAC,SAAA;IAAA;EAAA,GAmBpB;EAED,IAAIf,eAAe,KAAK,IAAI,EAAE;IAC5B,OAAO,IAAI;EACb;EAEA,OACEnC,IAAA,CAACd,aAAa;IAACY,KAAK,EAAEA,KAAM;IAAA8B,QAAA,EAC1B1B,KAAA,CAACpB,mBAAmB;MAAA8C,QAAA,GAClB5B,IAAA,CAACb,SAAS;QAACgE,KAAK,EAAC,OAAO;QAAC3B,eAAe,EAAE1B,KAAK,CAACsB,MAAM,CAACC;MAAQ,CAAE,CAAC,EAClErB,IAAA,CAACK,KAAK,CAACG,SAAS;QAACC,aAAa,EAAE;UAAE2C,WAAW,EAAE;QAAM,CAAE;QAAAxB,QAAA,EACpD,CAACO,eAAe,GACfnC,IAAA,CAACK,KAAK,CAACwB,MAAM;UAACX,IAAI,EAAC,OAAO;UAAAU,QAAA,EACvB,SAAAA,SAACyB,KAAK;YAAA,OAAKrD,IAAA,CAACV,WAAW,EAAAgE,aAAA,CAAAA,aAAA,KAAKD,KAAK;cAAEE,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQnB,kBAAkB,CAAC,IAAI,CAAC;cAAA;YAAC,EAAE,CAAC;UAAA;QAAA,CACnE,CAAC,GAEflC,KAAA,CAAAE,SAAA;UAAAwB,QAAA,GACE5B,IAAA,CAACK,KAAK,CAACwB,MAAM;YAACX,IAAI,EAAC,UAAU;YAACY,SAAS,EAAEvB;UAAS,CAAE,CAAC,EACrDP,IAAA,CAACK,KAAK,CAACwB,MAAM;YACXX,IAAI,EAAC,cAAc;YACnBY,SAAS,EAAElC,kBAAmB;YAC9B4D,OAAO,EAAE;cACPJ,WAAW,EAAE,IAAI;cACjBK,KAAK,EAAE,eAAe;cACtBlC,WAAW,EAAE;gBAAEC,eAAe,EAAE1B,KAAK,CAACsB,MAAM,CAACC;cAAQ,CAAC;cACtDI,eAAe,EAAE;YACnB;UAAE,CACH,CAAC,EACFzB,IAAA,CAACK,KAAK,CAACwB,MAAM;YACXX,IAAI,EAAC,WAAW;YAChBY,SAAS,EAAEjC,mBAAoB;YAC/B2D,OAAO,EAAE;cACPJ,WAAW,EAAE,IAAI;cACjBK,KAAK,EAAE,kBAAkB;cACzBlC,WAAW,EAAE;gBAAEC,eAAe,EAAE1B,KAAK,CAACsB,MAAM,CAACC;cAAQ,CAAC;cACtDI,eAAe,EAAE;YACnB;UAAE,CACH,CAAC;QAAA,CACF;MACH,CACc,CAAC;IAAA,CACC;EAAC,CACT,CAAC;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}