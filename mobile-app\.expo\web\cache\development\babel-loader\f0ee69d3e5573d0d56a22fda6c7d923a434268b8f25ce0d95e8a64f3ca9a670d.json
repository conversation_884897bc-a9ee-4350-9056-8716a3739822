{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedInterpolation from \"./AnimatedInterpolation\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nvar AnimatedModulo = function (_AnimatedWithChildren) {\n  function AnimatedModulo(a, modulus) {\n    var _this;\n    _classCallCheck(this, AnimatedModulo);\n    _this = _callSuper(this, AnimatedModulo);\n    _this._a = a;\n    _this._modulus = modulus;\n    return _this;\n  }\n  _inherits(AnimatedModulo, _AnimatedWithChildren);\n  return _createClass(AnimatedModulo, [{\n    key: \"__makeNative\",\n    value: function __makeNative(platformConfig) {\n      this._a.__makeNative(platformConfig);\n      _superPropGet(AnimatedModulo, \"__makeNative\", this, 3)([platformConfig]);\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      return (this._a.__getValue() % this._modulus + this._modulus) % this._modulus;\n    }\n  }, {\n    key: \"interpolate\",\n    value: function interpolate(config) {\n      return new AnimatedInterpolation(this, config);\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      this._a.__addChild(this);\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      this._a.__removeChild(this);\n      _superPropGet(AnimatedModulo, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      return {\n        type: 'modulus',\n        input: this._a.__getNativeTag(),\n        modulus: this._modulus\n      };\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedModulo;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedInterpolation", "AnimatedWithChildren", "AnimatedModulo", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "modulus", "_this", "_a", "_modulus", "key", "value", "__makeNative", "platformConfig", "__getValue", "interpolate", "config", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag"], "sources": ["C:/laragon/www/mobile-app/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedModulo.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nclass AnimatedModulo extends AnimatedWithChildren {\n  constructor(a, modulus) {\n    super();\n    this._a = a;\n    this._modulus = modulus;\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  __getValue() {\n    return (this._a.__getValue() % this._modulus + this._modulus) % this._modulus;\n  }\n  interpolate(config) {\n    return new AnimatedInterpolation(this, config);\n  }\n  __attach() {\n    this._a.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'modulus',\n      input: this._a.__getNativeTag(),\n      modulus: this._modulus\n    };\n  }\n}\nexport default AnimatedModulo;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,qBAAqB;AAC5B,OAAOC,oBAAoB;AAA+B,IACpDC,cAAc,aAAAC,qBAAA;EAClB,SAAAD,eAAYE,CAAC,EAAEC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA5B,eAAA,OAAAwB,cAAA;IACtBI,KAAA,GAAAtB,UAAA,OAAAkB,cAAA;IACAI,KAAA,CAAKC,EAAE,GAAGH,CAAC;IACXE,KAAA,CAAKE,QAAQ,GAAGH,OAAO;IAAC,OAAAC,KAAA;EAC1B;EAACvB,SAAA,CAAAmB,cAAA,EAAAC,qBAAA;EAAA,OAAAxB,YAAA,CAAAuB,cAAA;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpCf,aAAA,CAAAK,cAAA,4BAAmBU,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAG,UAAUA,CAAA,EAAG;MACX,OAAO,CAAC,IAAI,CAACN,EAAE,CAACM,UAAU,CAAC,CAAC,GAAG,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ;IAC/E;EAAC;IAAAC,GAAA;IAAAC,KAAA,EACD,SAAAI,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAIf,qBAAqB,CAAC,IAAI,EAAEe,MAAM,CAAC;IAChD;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAM,QAAQA,CAAA,EAAG;MACT,IAAI,CAACT,EAAE,CAACU,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAQ,QAAQA,CAAA,EAAG;MACT,IAAI,CAACX,EAAE,CAACY,aAAa,CAAC,IAAI,CAAC;MAC3BtB,aAAA,CAAAK,cAAA;IACF;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAU,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI,CAACf,EAAE,CAACgB,cAAc,CAAC,CAAC;QAC/BlB,OAAO,EAAE,IAAI,CAACG;MAChB,CAAC;IACH;EAAC;AAAA,EA7B0BP,oBAAoB;AA+BjD,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}