{"ast": null, "code": "export { default as AntDesign } from \"./AntDesign\";\nexport { default as <PERSON><PERSON><PERSON> } from \"./Entypo\";\nexport { default as EvilIcons } from \"./EvilIcons\";\nexport { default as Feather } from \"./Feather\";\nexport { default as <PERSON>ontisto } from \"./Fontisto\";\nexport { default as FontAwesome } from \"./FontAwesome\";\nexport { default as FontAwesome5 } from \"./FontAwesome5\";\nexport { default as Foundation } from \"./Foundation\";\nexport { default as Ionicons } from \"./Ionicons\";\nexport { default as MaterialCommunityIcons } from \"./MaterialCommunityIcons\";\nexport { default as MaterialIcons } from \"./MaterialIcons\";\nexport { default as Octicons } from \"./Octicons\";\nexport { default as SimpleLineIcons } from \"./SimpleLineIcons\";\nexport { default as Zocial } from \"./Zocial\";\nexport { default as createMultiStyleIconSet } from \"./createMultiStyleIconSet\";\nexport { default as createIconSet } from \"./createIconSet\";\nexport { default as createIconSetFromFontello } from \"./createIconSetFromFontello\";\nexport { default as createIconSetFromIcoMoon } from \"./createIconSetFromIcoMoon\";\nexport { default as createMu } from \"./createIconSetFromIcoMoon\";", "map": {"version": 3, "names": ["default", "AntDesign", "<PERSON><PERSON><PERSON>", "EvilIcons", "<PERSON><PERSON>", "Fontisto", "FontAwesome", "FontAwesome5", "Foundation", "Ionicons", "MaterialCommunityIcons", "MaterialIcons", "Octicons", "SimpleLineIcons", "Zocial", "createMultiStyleIconSet", "createIconSet", "createIconSetFromFontello", "createIconSetFromIcoMoon", "createMu"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\@expo\\vector-icons\\src\\Icons.ts"], "sourcesContent": ["export { default as AntDesign } from \"./AntDesign\";\nexport { default as <PERSON><PERSON><PERSON> } from \"./Entypo\";\nexport { default as EvilIcons } from \"./EvilIcons\";\nexport { default as Feather } from \"./Feather\";\nexport { default as <PERSON>ontisto } from \"./Fontisto\";\nexport { default as FontAwesome } from \"./FontAwesome\";\nexport { default as FontAwesome5 } from \"./FontAwesome5\";\nexport { default as Foundation } from \"./Foundation\";\nexport { default as Ionicons } from \"./Ionicons\";\nexport { default as MaterialCommunityIcons } from \"./MaterialCommunityIcons\";\nexport { default as MaterialIcons } from \"./MaterialIcons\";\nexport { default as Octicons } from \"./Octicons\";\nexport { default as SimpleLineIcons } from \"./SimpleLineIcons\";\nexport { default as Zocial } from \"./Zocial\";\nexport { default as createMultiStyleIconSet } from \"./createMultiStyleIconSet\";\nexport { default as createIconSet } from \"./createIconSet\";\nexport { default as createIconSetFromFontello } from \"./createIconSetFromFontello\";\nexport { default as createIconSetFromIcoMoon } from \"./createIconSetFromIcoMoon\";\nexport { default as createMu } from \"./createIconSetFromIcoMoon\";\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,SAAS;AAC7B,SAASD,OAAO,IAAIE,MAAM;AAC1B,SAASF,OAAO,IAAIG,SAAS;AAC7B,SAASH,OAAO,IAAII,OAAO;AAC3B,SAASJ,OAAO,IAAIK,QAAQ;AAC5B,SAASL,OAAO,IAAIM,WAAW;AAC/B,SAASN,OAAO,IAAIO,YAAY;AAChC,SAASP,OAAO,IAAIQ,UAAU;AAC9B,SAASR,OAAO,IAAIS,QAAQ;AAC5B,SAAST,OAAO,IAAIU,sBAAsB;AAC1C,SAASV,OAAO,IAAIW,aAAa;AACjC,SAASX,OAAO,IAAIY,QAAQ;AAC5B,SAASZ,OAAO,IAAIa,eAAe;AACnC,SAASb,OAAO,IAAIc,MAAM;AAC1B,SAASd,OAAO,IAAIe,uBAAuB;AAC3C,SAASf,OAAO,IAAIgB,aAAa;AACjC,SAAShB,OAAO,IAAIiB,yBAAyB;AAC7C,SAASjB,OAAO,IAAIkB,wBAAwB;AAC5C,SAASlB,OAAO,IAAImB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}