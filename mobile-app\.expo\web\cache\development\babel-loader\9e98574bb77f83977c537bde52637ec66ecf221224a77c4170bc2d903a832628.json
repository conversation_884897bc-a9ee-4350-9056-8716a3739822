{"ast": null, "code": "import EventEmitter from \"../vendor/emitter/EventEmitter\";\nexport default new EventEmitter();", "map": {"version": 3, "names": ["EventEmitter"], "sources": ["C:/laragon/www/mobile-app/node_modules/react-native-web/dist/vendor/react-native/EventEmitter/RCTDeviceEventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport EventEmitter from '../vendor/emitter/EventEmitter';\n\n// FIXME: use typed events\n\n/**\n * Global EventEmitter used by the native platform to emit events to JavaScript.\n * Events are identified by globally unique event names.\n *\n * NativeModules that emit events should instead subclass `NativeEventEmitter`.\n */\nexport default new EventEmitter();"], "mappings": "AAUA,OAAOA,YAAY;AAUnB,eAAe,IAAIA,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}