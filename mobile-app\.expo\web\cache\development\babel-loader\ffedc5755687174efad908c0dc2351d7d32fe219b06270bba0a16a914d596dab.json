{"ast": null, "code": "export default function ExpoStatusBar(props) {\n  return null;\n}", "map": {"version": 3, "names": ["ExpoStatusBar", "props"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\expo-status-bar\\src\\ExpoStatusBar.tsx"], "sourcesContent": ["import { StatusBarProps } from './StatusBar.types';\n\n// @needsAudit\n/**\n * A component that allows you to configure your status bar without directly calling imperative\n * methods like `setBarStyle`.\n *\n * You will likely have multiple `StatusBar` components mounted in the same app at the same time.\n * For example, if you have multiple screens in your app, you may end up using one per screen.\n * The props of each `StatusBar` component will be merged in the order that they were mounted.\n * This component is built on top of the [StatusBar](https://reactnative.dev/docs/statusbar)\n * component exported from React Native, and it provides defaults that work better for Expo users.\n */\nexport default function ExpoStatusBar(props: StatusBarProps) {\n  // StatusBar does nothing on web currently\n  return null;\n}\n"], "mappings": "AAaA,eAAc,SAAUA,aAAaA,CAACC,KAAqB;EAEzD,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}