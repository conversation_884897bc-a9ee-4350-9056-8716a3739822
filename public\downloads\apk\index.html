<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download APK - Bol<PERSON> Cambista</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .app-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 4px solid #1e40af;
        }
        
        .app-info h2 {
            color: #1e40af;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        
        .info-value {
            color: #1e40af;
            font-weight: 500;
        }
        
        .download-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(5, 150, 105, 0.4);
        }
        
        .download-icon {
            width: 24px;
            height: 24px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #059669;
        }
        
        .feature h3 {
            color: #1e40af;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .feature p {
            color: #6b7280;
            line-height: 1.5;
        }
        
        .requirements {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .requirements h3 {
            color: #92400e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .requirements ul {
            list-style: none;
            color: #92400e;
        }
        
        .requirements li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .requirements li::before {
            content: "⚠️";
        }
        
        .instructions {
            background: #ecfdf5;
            border: 1px solid #059669;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .instructions h3 {
            color: #065f46;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            color: #065f46;
            padding-left: 20px;
        }
        
        .instructions li {
            padding: 5px 0;
            line-height: 1.5;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .manual-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #1e40af;
            text-decoration: none;
            margin-top: 20px;
            padding: 10px 20px;
            border: 2px solid #1e40af;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .manual-link:hover {
            background: #1e40af;
            color: white;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Bolão Cambista</h1>
            <p>Aplicativo Móvel para Cambistas</p>
        </div>
        
        <div class="content">
            <div class="app-info">
                <h2>📊 Informações do Aplicativo</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Nome:</span>
                        <span class="info-value">Bolão Cambista</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Versão:</span>
                        <span class="info-value">1.0.0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Build:</span>
                        <span class="info-value">2</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Plataforma:</span>
                        <span class="info-value">Android 5.0+</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tamanho:</span>
                        <span class="info-value">~50MB</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Data:</span>
                        <span class="info-value" id="build-date">29/07/2025</span>
                    </div>
                </div>
            </div>
            
            <div class="download-section">
                <h2 style="margin-bottom: 20px; color: #1e40af;">📥 Download do APK Funcional</h2>
       <p style="color: #059669; font-weight: bold; margin-bottom: 15px;">✅ APK Real e Funcional - Pronto para Instalação!</p>
                <a href="bolao-cambista-v1.0.0-funcional.apk" class="download-btn" download>
                    <svg class="download-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Baixar APK
                </a>
                
                <a href="INSTALACAO.md" class="manual-link" target="_blank">
                    📖 Manual de Instalação
                </a>
            </div>
            
            <div class="features">
                <div class="feature">
                    <h3>🔐 Login Seguro</h3>
                    <p>Acesso exclusivo para cambistas autorizados com autenticação JWT</p>
                </div>
                <div class="feature">
                    <h3>📊 Dashboard</h3>
                    <p>Estatísticas em tempo real de usuários, bilhetes e vendas</p>
                </div>
                <div class="feature">
                    <h3>👥 Gestão de Usuários</h3>
                    <p>Controle completo de usuários e gerenciamento de saldo</p>
                </div>
                <div class="feature">
                    <h3>🎫 Bilhetes</h3>
                    <p>Criação e gerenciamento de bilhetes de apostas</p>
                </div>
                <div class="feature">
                    <h3>📱 Scanner QR</h3>
                    <p>Scanner QR Code integrado para facilitar operações</p>
                </div>
                <div class="feature">
                    <h3>📈 Relatórios</h3>
                    <p>Relatórios detalhados de vendas e comissões</p>
                </div>
            </div>
            
            <div class="requirements">
                <h3>⚠️ Requisitos do Sistema</h3>
                <ul>
                    <li>Android 5.0 ou superior</li>
                    <li>2GB de RAM mínimo</li>
                    <li>100MB de espaço livre</li>
                    <li>Conexão com internet ativa</li>
                </ul>
            </div>
            
            <div class="instructions">
                <h3>🔧 Como Instalar</h3>
                <ol>
                    <li><strong>Baixar:</strong> Clique no botão "Baixar APK" acima</li>
                    <li><strong>Permitir:</strong> Ative "Fontes desconhecidas" nas configurações</li>
                    <li><strong>Instalar:</strong> Abra o arquivo APK baixado</li>
                    <li><strong>Login:</strong> Use suas credenciais de cambista</li>
                    <li><strong>Usar:</strong> Aproveite todas as funcionalidades!</li>
                </ol>
            </div>
        </div>
        
        <div class="footer">
            <p>Sistema Bolão - Aplicativo para Cambistas</p>
            <p>Gerado automaticamente em <span id="current-date"></span></p>
        </div>
    </div>
    
    <script>
        // Definir data atual
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('pt-BR');
        document.getElementById('build-date').textContent = new Date().toLocaleDateString('pt-BR');
        
        // Adicionar evento de download
        document.querySelector('.download-btn').addEventListener('click', function() {
            // Analytics ou tracking podem ser adicionados aqui
            console.log('APK download iniciado');
        });
    </script>
</body>
</html>
