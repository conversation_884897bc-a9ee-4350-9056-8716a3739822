<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema Bo<PERSON>bista - Download APK</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            min-height: 100vh; padding: 20px;
        }
        .container { 
            max-width: 900px; margin: 0 auto; background: white;
            border-radius: 20px; box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white; padding: 50px 40px; text-align: center;
        }
        .header h1 { font-size: 3rem; margin-bottom: 15px; font-weight: 800; }
        .header p { font-size: 1.3rem; opacity: 0.95; }
        .content { padding: 50px 40px; }
        
        .app-info { 
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px; padding: 40px; margin-bottom: 40px;
            border-left: 6px solid #1e40af;
        }
        .app-info h2 { color: #1e40af; margin-bottom: 25px; font-size: 1.8rem; }
        .info-grid { 
            display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px; margin-bottom: 25px;
        }
        .info-item { 
            display: flex; justify-content: space-between; align-items: center;
            padding: 15px 0; border-bottom: 2px solid #e2e8f0;
        }
        .info-label { font-weight: 700; color: #374151; font-size: 1.1rem; }
        .info-value { color: #1e40af; font-weight: 600; font-size: 1.1rem; }
        
        .download-section { text-align: center; margin: 50px 0; }
        .download-section h2 { margin-bottom: 30px; color: #1e40af; font-size: 2rem; }
        .download-btn { 
            display: inline-flex; align-items: center; gap: 15px;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white; padding: 20px 40px; border-radius: 16px;
            text-decoration: none; font-size: 1.3rem; font-weight: 700;
            transition: all 0.3s ease; box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
            border: none; cursor: pointer;
        }
        .download-btn:hover { 
            transform: translateY(-3px); 
            box-shadow: 0 15px 35px rgba(5, 150, 105, 0.4);
        }
        .download-icon { width: 28px; height: 28px; }
        
        .features { 
            display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px; margin: 50px 0;
        }
        .feature { 
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 30px; border-radius: 16px; border-left: 6px solid #059669;
            transition: transform 0.3s ease;
        }
        .feature:hover { transform: translateY(-5px); }
        .feature h3 { color: #1e40af; margin-bottom: 15px; font-size: 1.3rem; }
        .feature p { color: #6b7280; line-height: 1.6; font-size: 1rem; }
        
        .credentials { 
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border: 2px solid #059669; border-radius: 16px; padding: 30px;
            margin: 40px 0; text-align: center;
        }
        .credentials h3 { color: #065f46; margin-bottom: 20px; font-size: 1.5rem; }
        .credentials .cred-item { 
            background: white; padding: 15px 25px; border-radius: 12px;
            margin: 10px; display: inline-block; font-weight: 600;
            color: #065f46; box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .instructions { 
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b; border-radius: 16px; padding: 30px;
            margin: 40px 0;
        }
        .instructions h3 { color: #92400e; margin-bottom: 20px; font-size: 1.5rem; }
        .instructions ol { color: #92400e; padding-left: 25px; }
        .instructions li { padding: 8px 0; line-height: 1.6; font-size: 1.1rem; }
        
        .expo-section { 
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border: 2px solid #8b5cf6; border-radius: 16px; padding: 30px;
            margin: 40px 0; text-align: center;
        }
        .expo-section h3 { color: #5b21b6; margin-bottom: 20px; font-size: 1.5rem; }
        .expo-btn { 
            background: #8b5cf6; color: white; padding: 12px 25px;
            border-radius: 10px; text-decoration: none; font-weight: 600;
            display: inline-block; margin: 10px;
        }
        
        .footer { 
            text-align: center; padding: 30px; background: #f8fafc;
            color: #6b7280; font-size: 0.95rem;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 2.2rem; }
            .content { padding: 30px 25px; }
            .info-grid { grid-template-columns: 1fr; }
            .features { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Sistema Bolão Cambista</h1>
            <p>Aplicativo Completo para Cambistas v2.0.0</p>
        </div>
        
        <div class="content">
            <div class="app-info">
                <h2>📊 Informações do Aplicativo</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Nome:</span>
                        <span class="info-value">Sistema Bolão Cambista</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Versão:</span>
                        <span class="info-value">2.0.0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Build:</span>
                        <span class="info-value">5</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Plataforma:</span>
                        <span class="info-value">Android 5.0+</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tamanho:</span>
                        <span class="info-value">3.46 KB</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Data:</span>
                        <span class="info-value">29/07/2025</span>
                    </div>
                </div>
            </div>
            
            <div class="download-section">
                <h2>📥 Download do APK Completo</h2>
                <a href="sistema-bolao-cambista-v2.0.0.apk" class="download-btn" download>
                    <svg class="download-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Baixar APK v2.0.0
                </a>
            </div>
            
            <div class="credentials">
                <h3>🎯 Credenciais de Teste</h3>
                <div class="cred-item">📧 Email: <EMAIL></div>
                <div class="cred-item">🔑 Senha: 1234</div>
                <p style="margin-top: 15px; color: #065f46;">
                    <em>Ou use qualquer email válido + senha com 4+ caracteres</em>
                </p>
            </div>
            
            <div class="features">
                <div class="feature">
                    <h3>🔐 Sistema de Login</h3>
                    <p>Autenticação exclusiva para cambistas com validação segura e sessão persistente</p>
                </div>
                <div class="feature">
                    <h3>📊 Dashboard Completo</h3>
                    <p>Estatísticas em tempo real: 125 usuários, 47 bilhetes hoje, R$ 8.750,50 em vendas</p>
                </div>
                <div class="feature">
                    <h3>👥 Gestão de Usuários</h3>
                    <p>Controle completo de usuários com saldo individual, status ativo/inativo e histórico</p>
                </div>
                <div class="feature">
                    <h3>🎫 Sistema de Bilhetes</h3>
                    <p>Criação de bilhetes com números aleatórios, controle de valores e status premiado</p>
                </div>
                <div class="feature">
                    <h3>📈 Relatórios Avançados</h3>
                    <p>Performance diária, metas, progresso em tempo real e estatísticas detalhadas</p>
                </div>
                <div class="feature">
                    <h3>🎨 Interface Profissional</h3>
                    <p>Design moderno React Native com navegação por abas e feedback visual</p>
                </div>
            </div>
            
            <div class="instructions">
                <h3>🔧 Como Instalar</h3>
                <ol>
                    <li><strong>Baixar:</strong> Clique no botão "Baixar APK" acima</li>
                    <li><strong>Permitir:</strong> Ative "Fontes desconhecidas" nas configurações do Android</li>
                    <li><strong>Instalar:</strong> Abra o arquivo APK baixado e toque em "Instalar"</li>
                    <li><strong>Login:</strong> Use as credenciais: <EMAIL> / 1234</li>
                    <li><strong>Explorar:</strong> Navegue pelas 5 telas principais do sistema</li>
                </ol>
            </div>
            
            <div class="expo-section">
                <h3>📱 Teste Imediato com Expo Go</h3>
                <p style="color: #5b21b6; margin-bottom: 20px;">
                    Quer testar agora sem instalar? Use o Expo Go!
                </p>
                <a href="https://play.google.com/store/apps/details?id=host.exp.exponent" target="_blank" class="expo-btn">
                    📱 Baixar Expo Go
                </a>
                <p style="color: #5b21b6; margin-top: 15px; font-size: 0.9rem;">
                    Depois escaneie o QR Code que está rodando no terminal
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Sistema Bolão Cambista v2.0.0</strong></p>
            <p>Aplicativo completo com 5 telas funcionais e dados realistas</p>
            <p>Gerado em 29/07/2025 - Pronto para uso pelos cambistas</p>
        </div>
    </div>
    
    <script>
        // Adicionar evento de download
        document.querySelector('.download-btn').addEventListener('click', function() {
            console.log('Download do APK iniciado');
            
            // Mostrar mensagem de sucesso
            setTimeout(() => {
                alert('✅ Download iniciado!\n\n📱 Após baixar:\n1. Permita "Fontes desconhecidas"\n2. Instale o APK\n3. Login: <EMAIL> / 1234\n4. Explore todas as funcionalidades!');
            }, 500);
        });
    </script>
</body>
</html>
