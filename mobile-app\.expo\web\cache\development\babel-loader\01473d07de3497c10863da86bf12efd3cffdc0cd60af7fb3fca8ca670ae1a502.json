{"ast": null, "code": "import StatusBar from \"react-native-web/dist/exports/StatusBar\";\nexport default function setStatusBarNetworkActivityIndicatorVisible(visible) {\n  StatusBar.setNetworkActivityIndicatorVisible(visible);\n}", "map": {"version": 3, "names": ["setStatusBarNetworkActivityIndicatorVisible", "visible", "StatusBar", "setNetworkActivityIndicatorVisible"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\expo-status-bar\\src\\setStatusBarNetworkActivityIndicatorVisible.ts"], "sourcesContent": ["import { StatusBar } from 'react-native';\n\n// @needsAudit\n/**\n * Toggle visibility of the network activity indicator.\n * @param visible If the network activity indicator should be visible.\n * @platform ios\n */\nexport default function setStatusBarNetworkActivityIndicatorVisible(visible: boolean) {\n  StatusBar.setNetworkActivityIndicatorVisible(visible);\n}\n"], "mappings": ";AAQA,eAAc,SAAUA,2CAA2CA,CAACC,OAAgB;EAClFC,SAAS,CAACC,kCAAkC,CAACF,OAAO,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}