{"ast": null, "code": "import createIconSet<PERSON><PERSON><PERSON><PERSON><PERSON> from \"./vendor/react-native-vector-icons/lib/create-icon-set-from-font<PERSON>\";\nexport default function (config, expoFontName, expoAssetId) {\n  return createIconSetFromFontello(config, expoFontName, expoAssetId);\n}", "map": {"version": 3, "names": ["createIconSetFromFontello", "config", "expoFontName", "expoAssetId"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\@expo\\vector-icons\\src\\createIconSetFromFontello.ts"], "sourcesContent": ["import createIconSet<PERSON><PERSON><PERSON><PERSON>llo from './vendor/react-native-vector-icons/lib/create-icon-set-from-fontello';\n\nexport default function(config, expoFontName, expoAssetId) {\n  return createIconSetFromFontello(config, expoFontName, expoAssetId);\n}\n"], "mappings": "AAAA,OAAOA,yBAAyB;AAEhC,eAAc,UAAUC,MAAM,EAAEC,YAAY,EAAEC,WAAW;EACvD,OAAOH,yBAAyB,CAACC,MAAM,EAAEC,YAAY,EAAEC,WAAW,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}