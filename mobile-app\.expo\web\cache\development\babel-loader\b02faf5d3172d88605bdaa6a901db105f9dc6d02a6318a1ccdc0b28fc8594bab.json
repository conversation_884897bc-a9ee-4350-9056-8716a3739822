{"ast": null, "code": "import unitlessNumbers from \"./unitlessNumbers\";\nimport normalizeColor from \"./normalizeColor\";\nvar colorProps = {\n  backgroundColor: true,\n  borderColor: true,\n  borderTopColor: true,\n  borderRightColor: true,\n  borderBottomColor: true,\n  borderLeftColor: true,\n  color: true,\n  shadowColor: true,\n  textDecorationColor: true,\n  textShadowColor: true\n};\nexport default function normalizeValueWithProperty(value, property) {\n  var returnValue = value;\n  if ((property == null || !unitlessNumbers[property]) && typeof value === 'number') {\n    returnValue = value + \"px\";\n  } else if (property != null && colorProps[property]) {\n    returnValue = normalizeColor(value);\n  }\n  return returnValue;\n}", "map": {"version": 3, "names": ["unitlessNumbers", "normalizeColor", "colorProps", "backgroundColor", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "color", "shadowColor", "textDecorationColor", "textShadowColor", "normalizeValueWithProperty", "value", "property", "returnValue"], "sources": ["C:/laragon/www/mobile-app/node_modules/react-native-web/dist/exports/StyleSheet/compiler/normalizeValueWithProperty.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport unitlessNumbers from './unitlessNumbers';\nimport normalizeColor from './normalizeColor';\nvar colorProps = {\n  backgroundColor: true,\n  borderColor: true,\n  borderTopColor: true,\n  borderRightColor: true,\n  borderBottomColor: true,\n  borderLeftColor: true,\n  color: true,\n  shadowColor: true,\n  textDecorationColor: true,\n  textShadowColor: true\n};\nexport default function normalizeValueWithProperty(value, property) {\n  var returnValue = value;\n  if ((property == null || !unitlessNumbers[property]) && typeof value === 'number') {\n    returnValue = value + \"px\";\n  } else if (property != null && colorProps[property]) {\n    returnValue = normalizeColor(value);\n  }\n  return returnValue;\n}"], "mappings": "AASA,OAAOA,eAAe;AACtB,OAAOC,cAAc;AACrB,IAAIC,UAAU,GAAG;EACfC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,IAAI;EACjBC,mBAAmB,EAAE,IAAI;EACzBC,eAAe,EAAE;AACnB,CAAC;AACD,eAAe,SAASC,0BAA0BA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAClE,IAAIC,WAAW,GAAGF,KAAK;EACvB,IAAI,CAACC,QAAQ,IAAI,IAAI,IAAI,CAACf,eAAe,CAACe,QAAQ,CAAC,KAAK,OAAOD,KAAK,KAAK,QAAQ,EAAE;IACjFE,WAAW,GAAGF,KAAK,GAAG,IAAI;EAC5B,CAAC,MAAM,IAAIC,QAAQ,IAAI,IAAI,IAAIb,UAAU,CAACa,QAAQ,CAAC,EAAE;IACnDC,WAAW,GAAGf,cAAc,CAACa,KAAK,CAAC;EACrC;EACA,OAAOE,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}