{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Card, Title, Paragraph, Button, FAB, Text, Chip, ActivityIndicator } from 'react-native-paper';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { Ionicons } from '@expo/vector-icons';\nimport { theme } from \"../theme/theme\";\nimport { apiService } from \"../services/api\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function HomeScreen(_ref) {\n  var navigation = _ref.navigation;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    user = _useState2[0],\n    setUser = _useState2[1];\n  var _useState3 = useState({\n      totalUsuarios: 0,\n      bilhetesHoje: 0,\n      vendasHoje: 0,\n      comissaoMes: 0\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    stats = _useState4[0],\n    setStats = _useState4[1];\n  var _useState5 = useState(true),\n    _useState6 = _slicedToArray(_useState5, 2),\n    loading = _useState6[0],\n    setLoading = _useState6[1];\n  var _useState7 = useState(false),\n    _useState8 = _slicedToArray(_useState7, 2),\n    refreshing = _useState8[0],\n    setRefreshing = _useState8[1];\n  useEffect(function () {\n    loadData();\n  }, []);\n  var loadData = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        var currentUser = yield apiService.getCurrentUser();\n        if (currentUser) {\n          setUser(currentUser);\n          yield loadStats(currentUser.id);\n        }\n      } catch (error) {\n        Alert.alert('Erro', 'Erro ao carregar dados');\n      } finally {\n        setLoading(false);\n      }\n    });\n    return function loadData() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var loadStats = function () {\n    var _ref3 = _asyncToGenerator(function* (cambistaId) {\n      try {\n        var response = yield apiService.getRelatorios(cambistaId, 'hoje');\n        if (response.success && response.data) {\n          setStats({\n            totalUsuarios: response.data.totalUsuarios || 0,\n            bilhetesHoje: response.data.bilhetesHoje || 0,\n            vendasHoje: response.data.vendasHoje || 0,\n            comissaoMes: response.data.comissaoMes || 0\n          });\n        }\n      } catch (error) {\n        console.error('Erro ao carregar estatísticas:', error);\n      }\n    });\n    return function loadStats(_x) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var onRefresh = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      setRefreshing(true);\n      yield loadData();\n      setRefreshing(false);\n    });\n    return function onRefresh() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var formatCurrency = function formatCurrency(value) {\n    return new Intl.NumberFormat('pt-BR', {\n      style: 'currency',\n      currency: 'BRL'\n    }).format(value);\n  };\n  if (loading) {\n    return _jsxs(View, {\n      style: styles.loadingContainer,\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: theme.colors.primary\n      }), _jsx(Text, {\n        style: styles.loadingText,\n        children: \"Carregando...\"\n      })]\n    });\n  }\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(LinearGradient, {\n      colors: [theme.colors.primary, '#3b82f6'],\n      style: styles.header,\n      children: _jsxs(View, {\n        style: styles.headerContent,\n        children: [_jsxs(Title, {\n          style: styles.headerTitle,\n          children: [\"Ol\\xE1, \", user == null ? void 0 : user.nome, \"!\"]\n        }), _jsxs(Paragraph, {\n          style: styles.headerSubtitle,\n          children: [\"Cambista \\u2022 \", new Date().toLocaleDateString('pt-BR')]\n        }), _jsx(Chip, {\n          icon: \"account-check\",\n          style: styles.statusChip,\n          textStyle: styles.statusChipText,\n          children: \"Ativo\"\n        })]\n      })\n    }), _jsxs(ScrollView, {\n      style: styles.content,\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      children: [_jsxs(View, {\n        style: styles.statsContainer,\n        children: [_jsxs(View, {\n          style: styles.statsRow,\n          children: [_jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#059669'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statCardContent,\n              children: [_jsx(Ionicons, {\n                name: \"people\",\n                size: 24,\n                color: \"#fff\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: stats.totalUsuarios\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"Usu\\xE1rios\"\n              })]\n            })\n          }), _jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#f59e0b'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statCardContent,\n              children: [_jsx(Ionicons, {\n                name: \"receipt\",\n                size: 24,\n                color: \"#fff\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: stats.bilhetesHoje\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"Bilhetes Hoje\"\n              })]\n            })\n          })]\n        }), _jsxs(View, {\n          style: styles.statsRow,\n          children: [_jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#3b82f6'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statCardContent,\n              children: [_jsx(Ionicons, {\n                name: \"cash\",\n                size: 24,\n                color: \"#fff\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: formatCurrency(stats.vendasHoje)\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"Vendas Hoje\"\n              })]\n            })\n          }), _jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#8b5cf6'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statCardContent,\n              children: [_jsx(Ionicons, {\n                name: \"trending-up\",\n                size: 24,\n                color: \"#fff\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: formatCurrency(stats.comissaoMes)\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"Comiss\\xE3o M\\xEAs\"\n              })]\n            })\n          })]\n        })]\n      }), _jsx(Card, {\n        style: styles.actionsCard,\n        children: _jsxs(Card.Content, {\n          children: [_jsx(Title, {\n            style: styles.actionsTitle,\n            children: \"A\\xE7\\xF5es R\\xE1pidas\"\n          }), _jsxs(View, {\n            style: styles.actionsContainer,\n            children: [_jsx(Button, {\n              mode: \"contained\",\n              icon: \"plus\",\n              style: [styles.actionButton, {\n                backgroundColor: theme.colors.secondary\n              }],\n              onPress: function onPress() {\n                return navigation.navigate('CriarBilhete');\n              },\n              children: \"Novo Bilhete\"\n            }), _jsx(Button, {\n              mode: \"outlined\",\n              icon: \"qrcode-scan\",\n              style: styles.actionButton,\n              onPress: function onPress() {\n                return navigation.navigate('QRScanner');\n              },\n              children: \"Escanear QR\"\n            })]\n          }), _jsxs(View, {\n            style: styles.actionsContainer,\n            children: [_jsx(Button, {\n              mode: \"outlined\",\n              icon: \"account-plus\",\n              style: styles.actionButton,\n              onPress: function onPress() {\n                return navigation.navigate('Usuários');\n              },\n              children: \"Gerenciar Usu\\xE1rios\"\n            }), _jsx(Button, {\n              mode: \"outlined\",\n              icon: \"chart-line\",\n              style: styles.actionButton,\n              onPress: function onPress() {\n                return navigation.navigate('Relatórios');\n              },\n              children: \"Ver Relat\\xF3rios\"\n            })]\n          })]\n        })\n      }), _jsx(Card, {\n        style: styles.infoCard,\n        children: _jsxs(Card.Content, {\n          children: [_jsx(Title, {\n            style: styles.infoTitle,\n            children: \"Sistema\"\n          }), _jsxs(Paragraph, {\n            style: styles.infoText,\n            children: [\"Vers\\xE3o: 1.0.0\", '\\n', \"\\xDAltima atualiza\\xE7\\xE3o: \", new Date().toLocaleDateString('pt-BR')]\n          })]\n        })\n      })]\n    }), _jsx(FAB, {\n      style: styles.fab,\n      icon: \"plus\",\n      onPress: function onPress() {\n        return navigation.navigate('CriarBilhete');\n      }\n    })]\n  });\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: theme.colors.background\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  loadingText: {\n    marginTop: 16,\n    color: theme.colors.text\n  },\n  header: {\n    paddingTop: 60,\n    paddingBottom: 30,\n    paddingHorizontal: 20\n  },\n  headerContent: {\n    alignItems: 'center'\n  },\n  headerTitle: {\n    color: '#fff',\n    fontSize: 24,\n    fontWeight: 'bold'\n  },\n  headerSubtitle: {\n    color: '#e2e8f0',\n    fontSize: 16,\n    marginTop: 4\n  },\n  statusChip: {\n    marginTop: 12,\n    backgroundColor: 'rgba(255, 255, 255, 0.2)'\n  },\n  statusChipText: {\n    color: '#fff'\n  },\n  content: {\n    flex: 1,\n    padding: 16\n  },\n  statsContainer: {\n    marginBottom: 20\n  },\n  statsRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginBottom: 12\n  },\n  statCard: {\n    flex: 1,\n    marginHorizontal: 6,\n    elevation: 4\n  },\n  statCardContent: {\n    alignItems: 'center',\n    paddingVertical: 16\n  },\n  statNumber: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginTop: 8\n  },\n  statLabel: {\n    fontSize: 12,\n    color: '#fff',\n    marginTop: 4\n  },\n  actionsCard: {\n    marginBottom: 20,\n    elevation: 4\n  },\n  actionsTitle: {\n    marginBottom: 16,\n    color: theme.colors.primary\n  },\n  actionsContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginBottom: 12\n  },\n  actionButton: {\n    flex: 1,\n    marginHorizontal: 6\n  },\n  infoCard: {\n    marginBottom: 20,\n    elevation: 2\n  },\n  infoTitle: {\n    fontSize: 18,\n    color: theme.colors.primary\n  },\n  infoText: {\n    color: theme.colors.onSurface,\n    marginTop: 8\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0,\n    backgroundColor: theme.colors.secondary\n  }\n});", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "StyleSheet", "ScrollView", "RefreshControl", "<PERSON><PERSON>", "Card", "Title", "Paragraph", "<PERSON><PERSON>", "FAB", "Text", "Chip", "ActivityIndicator", "LinearGradient", "Ionicons", "theme", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "HomeScreen", "_ref", "navigation", "_useState", "_useState2", "_slicedToArray", "user", "setUser", "_useState3", "totalUsuarios", "bilhetesHoje", "vendasHoje", "comissaoMes", "_useState4", "stats", "setStats", "_useState5", "_useState6", "loading", "setLoading", "_useState7", "_useState8", "refreshing", "setRefreshing", "loadData", "_ref2", "_asyncToGenerator", "currentUser", "getCurrentUser", "loadStats", "id", "error", "alert", "apply", "arguments", "_ref3", "cambistaId", "response", "getRelatorios", "success", "data", "console", "_x", "onRefresh", "_ref4", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "styles", "loadingContainer", "children", "size", "color", "colors", "primary", "loadingText", "container", "header", "headerContent", "headerTitle", "nome", "headerSubtitle", "Date", "toLocaleDateString", "icon", "statusChip", "textStyle", "statusChipText", "content", "refreshControl", "stats<PERSON><PERSON><PERSON>", "statsRow", "statCard", "backgroundColor", "Content", "statCardContent", "name", "statNumber", "statLabel", "actionsCard", "actionsTitle", "actionsContainer", "mode", "actionButton", "secondary", "onPress", "navigate", "infoCard", "infoTitle", "infoText", "fab", "create", "flex", "background", "justifyContent", "alignItems", "marginTop", "text", "paddingTop", "paddingBottom", "paddingHorizontal", "fontSize", "fontWeight", "padding", "marginBottom", "flexDirection", "marginHorizontal", "elevation", "paddingVertical", "onSurface", "position", "margin", "right", "bottom"], "sources": ["C:/laragon/www/mobile-app/src/screens/HomeScreen.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  StyleSheet,\n  ScrollView,\n  RefreshControl,\n  Alert,\n} from 'react-native';\nimport {\n  Card,\n  Title,\n  Paragraph,\n  Button,\n  FAB,\n  Text,\n  Chip,\n  ActivityIndicator,\n} from 'react-native-paper';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { Ionicons } from '@expo/vector-icons';\nimport { theme } from '../theme/theme';\nimport { apiService, User } from '../services/api';\n\nexport default function HomeScreen({ navigation }: any) {\n  const [user, setUser] = useState<User | null>(null);\n  const [stats, setStats] = useState({\n    totalUsuarios: 0,\n    bilhetesHoje: 0,\n    vendasHoje: 0,\n    comissaoMes: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      const currentUser = await apiService.getCurrentUser();\n      if (currentUser) {\n        setUser(currentUser);\n        await loadStats(currentUser.id);\n      }\n    } catch (error) {\n      Alert.alert('Erro', 'Erro ao carregar dados');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStats = async (cambistaId: number) => {\n    try {\n      const response = await apiService.getRelatorios(cambistaId, 'hoje');\n      if (response.success && response.data) {\n        setStats({\n          totalUsuarios: response.data.totalUsuarios || 0,\n          bilhetesHoje: response.data.bilhetesHoje || 0,\n          vendasHoje: response.data.vendasHoje || 0,\n          comissaoMes: response.data.comissaoMes || 0,\n        });\n      }\n    } catch (error) {\n      console.error('Erro ao carregar estatísticas:', error);\n    }\n  };\n\n  const onRefresh = async () => {\n    setRefreshing(true);\n    await loadData();\n    setRefreshing(false);\n  };\n\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('pt-BR', {\n      style: 'currency',\n      currency: 'BRL',\n    }).format(value);\n  };\n\n  if (loading) {\n    return (\n      <View style={styles.loadingContainer}>\n        <ActivityIndicator size=\"large\" color={theme.colors.primary} />\n        <Text style={styles.loadingText}>Carregando...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <View style={styles.container}>\n      <LinearGradient\n        colors={[theme.colors.primary, '#3b82f6']}\n        style={styles.header}\n      >\n        <View style={styles.headerContent}>\n          <Title style={styles.headerTitle}>Olá, {user?.nome}!</Title>\n          <Paragraph style={styles.headerSubtitle}>\n            Cambista • {new Date().toLocaleDateString('pt-BR')}\n          </Paragraph>\n          <Chip\n            icon=\"account-check\"\n            style={styles.statusChip}\n            textStyle={styles.statusChipText}\n          >\n            Ativo\n          </Chip>\n        </View>\n      </LinearGradient>\n\n      <ScrollView\n        style={styles.content}\n        refreshControl={\n          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />\n        }\n      >\n        {/* Cards de Estatísticas */}\n        <View style={styles.statsContainer}>\n          <View style={styles.statsRow}>\n            <Card style={[styles.statCard, { backgroundColor: '#059669' }]}>\n              <Card.Content style={styles.statCardContent}>\n                <Ionicons name=\"people\" size={24} color=\"#fff\" />\n                <Text style={styles.statNumber}>{stats.totalUsuarios}</Text>\n                <Text style={styles.statLabel}>Usuários</Text>\n              </Card.Content>\n            </Card>\n\n            <Card style={[styles.statCard, { backgroundColor: '#f59e0b' }]}>\n              <Card.Content style={styles.statCardContent}>\n                <Ionicons name=\"receipt\" size={24} color=\"#fff\" />\n                <Text style={styles.statNumber}>{stats.bilhetesHoje}</Text>\n                <Text style={styles.statLabel}>Bilhetes Hoje</Text>\n              </Card.Content>\n            </Card>\n          </View>\n\n          <View style={styles.statsRow}>\n            <Card style={[styles.statCard, { backgroundColor: '#3b82f6' }]}>\n              <Card.Content style={styles.statCardContent}>\n                <Ionicons name=\"cash\" size={24} color=\"#fff\" />\n                <Text style={styles.statNumber}>{formatCurrency(stats.vendasHoje)}</Text>\n                <Text style={styles.statLabel}>Vendas Hoje</Text>\n              </Card.Content>\n            </Card>\n\n            <Card style={[styles.statCard, { backgroundColor: '#8b5cf6' }]}>\n              <Card.Content style={styles.statCardContent}>\n                <Ionicons name=\"trending-up\" size={24} color=\"#fff\" />\n                <Text style={styles.statNumber}>{formatCurrency(stats.comissaoMes)}</Text>\n                <Text style={styles.statLabel}>Comissão Mês</Text>\n              </Card.Content>\n            </Card>\n          </View>\n        </View>\n\n        {/* Ações Rápidas */}\n        <Card style={styles.actionsCard}>\n          <Card.Content>\n            <Title style={styles.actionsTitle}>Ações Rápidas</Title>\n            \n            <View style={styles.actionsContainer}>\n              <Button\n                mode=\"contained\"\n                icon=\"plus\"\n                style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}\n                onPress={() => navigation.navigate('CriarBilhete')}\n              >\n                Novo Bilhete\n              </Button>\n\n              <Button\n                mode=\"outlined\"\n                icon=\"qrcode-scan\"\n                style={styles.actionButton}\n                onPress={() => navigation.navigate('QRScanner')}\n              >\n                Escanear QR\n              </Button>\n            </View>\n\n            <View style={styles.actionsContainer}>\n              <Button\n                mode=\"outlined\"\n                icon=\"account-plus\"\n                style={styles.actionButton}\n                onPress={() => navigation.navigate('Usuários')}\n              >\n                Gerenciar Usuários\n              </Button>\n\n              <Button\n                mode=\"outlined\"\n                icon=\"chart-line\"\n                style={styles.actionButton}\n                onPress={() => navigation.navigate('Relatórios')}\n              >\n                Ver Relatórios\n              </Button>\n            </View>\n          </Card.Content>\n        </Card>\n\n        {/* Informações do Sistema */}\n        <Card style={styles.infoCard}>\n          <Card.Content>\n            <Title style={styles.infoTitle}>Sistema</Title>\n            <Paragraph style={styles.infoText}>\n              Versão: 1.0.0{'\\n'}\n              Última atualização: {new Date().toLocaleDateString('pt-BR')}\n            </Paragraph>\n          </Card.Content>\n        </Card>\n      </ScrollView>\n\n      <FAB\n        style={styles.fab}\n        icon=\"plus\"\n        onPress={() => navigation.navigate('CriarBilhete')}\n      />\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: theme.colors.background,\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  loadingText: {\n    marginTop: 16,\n    color: theme.colors.text,\n  },\n  header: {\n    paddingTop: 60,\n    paddingBottom: 30,\n    paddingHorizontal: 20,\n  },\n  headerContent: {\n    alignItems: 'center',\n  },\n  headerTitle: {\n    color: '#fff',\n    fontSize: 24,\n    fontWeight: 'bold',\n  },\n  headerSubtitle: {\n    color: '#e2e8f0',\n    fontSize: 16,\n    marginTop: 4,\n  },\n  statusChip: {\n    marginTop: 12,\n    backgroundColor: 'rgba(255, 255, 255, 0.2)',\n  },\n  statusChipText: {\n    color: '#fff',\n  },\n  content: {\n    flex: 1,\n    padding: 16,\n  },\n  statsContainer: {\n    marginBottom: 20,\n  },\n  statsRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginBottom: 12,\n  },\n  statCard: {\n    flex: 1,\n    marginHorizontal: 6,\n    elevation: 4,\n  },\n  statCardContent: {\n    alignItems: 'center',\n    paddingVertical: 16,\n  },\n  statNumber: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginTop: 8,\n  },\n  statLabel: {\n    fontSize: 12,\n    color: '#fff',\n    marginTop: 4,\n  },\n  actionsCard: {\n    marginBottom: 20,\n    elevation: 4,\n  },\n  actionsTitle: {\n    marginBottom: 16,\n    color: theme.colors.primary,\n  },\n  actionsContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    marginBottom: 12,\n  },\n  actionButton: {\n    flex: 1,\n    marginHorizontal: 6,\n  },\n  infoCard: {\n    marginBottom: 20,\n    elevation: 2,\n  },\n  infoTitle: {\n    fontSize: 18,\n    color: theme.colors.primary,\n  },\n  infoText: {\n    color: theme.colors.onSurface,\n    marginTop: 8,\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0,\n    backgroundColor: theme.colors.secondary,\n  },\n});\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,cAAA;AAAA,OAAAC,KAAA;AAQnD,SACEC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,iBAAiB,QACZ,oBAAoB;AAC3B,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,KAAK;AACd,SAASC,UAAU;AAAgC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEnD,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAAsB;EAAA,IAAnBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAC7C,IAAAC,SAAA,GAAwB1B,QAAQ,CAAc,IAAI,CAAC;IAAA2B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA5CG,IAAI,GAAAF,UAAA;IAAEG,OAAO,GAAAH,UAAA;EACpB,IAAAI,UAAA,GAA0B/B,QAAQ,CAAC;MACjCgC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE;IACf,CAAC,CAAC;IAAAC,UAAA,GAAAR,cAAA,CAAAG,UAAA;IALKM,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EAMtB,IAAAG,UAAA,GAA8BvC,QAAQ,CAAC,IAAI,CAAC;IAAAwC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAoC3C,QAAQ,CAAC,KAAK,CAAC;IAAA4C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAEhC3C,SAAS,CAAC,YAAM;IACd8C,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,QAAQ;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAC3B,IAAI;QACF,IAAMC,WAAW,SAAShC,UAAU,CAACiC,cAAc,CAAC,CAAC;QACrD,IAAID,WAAW,EAAE;UACfpB,OAAO,CAACoB,WAAW,CAAC;UACpB,MAAME,SAAS,CAACF,WAAW,CAACG,EAAE,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdhD,KAAK,CAACiD,KAAK,CAAC,MAAM,EAAE,wBAAwB,CAAC;MAC/C,CAAC,SAAS;QACRb,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBAZKK,QAAQA,CAAA;MAAA,OAAAC,KAAA,CAAAQ,KAAA,OAAAC,SAAA;IAAA;EAAA,GAYb;EAED,IAAML,SAAS;IAAA,IAAAM,KAAA,GAAAT,iBAAA,CAAG,WAAOU,UAAkB,EAAK;MAC9C,IAAI;QACF,IAAMC,QAAQ,SAAS1C,UAAU,CAAC2C,aAAa,CAACF,UAAU,EAAE,MAAM,CAAC;QACnE,IAAIC,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;UACrCzB,QAAQ,CAAC;YACPN,aAAa,EAAE4B,QAAQ,CAACG,IAAI,CAAC/B,aAAa,IAAI,CAAC;YAC/CC,YAAY,EAAE2B,QAAQ,CAACG,IAAI,CAAC9B,YAAY,IAAI,CAAC;YAC7CC,UAAU,EAAE0B,QAAQ,CAACG,IAAI,CAAC7B,UAAU,IAAI,CAAC;YACzCC,WAAW,EAAEyB,QAAQ,CAACG,IAAI,CAAC5B,WAAW,IAAI;UAC5C,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACdU,OAAO,CAACV,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;IACF,CAAC;IAAA,gBAdKF,SAASA,CAAAa,EAAA;MAAA,OAAAP,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAcd;EAED,IAAMS,SAAS;IAAA,IAAAC,KAAA,GAAAlB,iBAAA,CAAG,aAAY;MAC5BH,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMC,QAAQ,CAAC,CAAC;MAChBD,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAAA,gBAJKoB,SAASA,CAAA;MAAA,OAAAC,KAAA,CAAAX,KAAA,OAAAC,SAAA;IAAA;EAAA,GAId;EAED,IAAMW,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAa,EAAK;IACxC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB,CAAC;EAED,IAAI5B,OAAO,EAAE;IACX,OACEnB,KAAA,CAACpB,IAAI;MAACsE,KAAK,EAAEG,MAAM,CAACC,gBAAiB;MAAAC,QAAA,GACnCzD,IAAA,CAACN,iBAAiB;QAACgE,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE9D,KAAK,CAAC+D,MAAM,CAACC;MAAQ,CAAE,CAAC,EAC/D7D,IAAA,CAACR,IAAI;QAAC4D,KAAK,EAAEG,MAAM,CAACO,WAAY;QAAAL,QAAA,EAAC;MAAa,CAAM,CAAC;IAAA,CACjD,CAAC;EAEX;EAEA,OACEvD,KAAA,CAACpB,IAAI;IAACsE,KAAK,EAAEG,MAAM,CAACQ,SAAU;IAAAN,QAAA,GAC5BzD,IAAA,CAACL,cAAc;MACbiE,MAAM,EAAE,CAAC/D,KAAK,CAAC+D,MAAM,CAACC,OAAO,EAAE,SAAS,CAAE;MAC1CT,KAAK,EAAEG,MAAM,CAACS,MAAO;MAAAP,QAAA,EAErBvD,KAAA,CAACpB,IAAI;QAACsE,KAAK,EAAEG,MAAM,CAACU,aAAc;QAAAR,QAAA,GAChCvD,KAAA,CAACd,KAAK;UAACgE,KAAK,EAAEG,MAAM,CAACW,WAAY;UAAAT,QAAA,GAAC,UAAK,EAAChD,IAAI,oBAAJA,IAAI,CAAE0D,IAAI,EAAC,GAAC;QAAA,CAAO,CAAC,EAC5DjE,KAAA,CAACb,SAAS;UAAC+D,KAAK,EAAEG,MAAM,CAACa,cAAe;UAAAX,QAAA,GAAC,kBAC5B,EAAC,IAAIY,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;QAAA,CACzC,CAAC,EACZtE,IAAA,CAACP,IAAI;UACH8E,IAAI,EAAC,eAAe;UACpBnB,KAAK,EAAEG,MAAM,CAACiB,UAAW;UACzBC,SAAS,EAAElB,MAAM,CAACmB,cAAe;UAAAjB,QAAA,EAClC;QAED,CAAM,CAAC;MAAA,CACH;IAAC,CACO,CAAC,EAEjBvD,KAAA,CAAClB,UAAU;MACToE,KAAK,EAAEG,MAAM,CAACoB,OAAQ;MACtBC,cAAc,EACZ5E,IAAA,CAACf,cAAc;QAACwC,UAAU,EAAEA,UAAW;QAACqB,SAAS,EAAEA;MAAU,CAAE,CAChE;MAAAW,QAAA,GAGDvD,KAAA,CAACpB,IAAI;QAACsE,KAAK,EAAEG,MAAM,CAACsB,cAAe;QAAApB,QAAA,GACjCvD,KAAA,CAACpB,IAAI;UAACsE,KAAK,EAAEG,MAAM,CAACuB,QAAS;UAAArB,QAAA,GAC3BzD,IAAA,CAACb,IAAI;YAACiE,KAAK,EAAE,CAACG,MAAM,CAACwB,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAvB,QAAA,EAC7DvD,KAAA,CAACf,IAAI,CAAC8F,OAAO;cAAC7B,KAAK,EAAEG,MAAM,CAAC2B,eAAgB;cAAAzB,QAAA,GAC1CzD,IAAA,CAACJ,QAAQ;gBAACuF,IAAI,EAAC,QAAQ;gBAACzB,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAM,CAAE,CAAC,EACjD3D,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC6B,UAAW;gBAAA3B,QAAA,EAAExC,KAAK,CAACL;cAAa,CAAO,CAAC,EAC5DZ,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC8B,SAAU;gBAAA5B,QAAA,EAAC;cAAQ,CAAM,CAAC;YAAA,CAClC;UAAC,CACX,CAAC,EAEPzD,IAAA,CAACb,IAAI;YAACiE,KAAK,EAAE,CAACG,MAAM,CAACwB,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAvB,QAAA,EAC7DvD,KAAA,CAACf,IAAI,CAAC8F,OAAO;cAAC7B,KAAK,EAAEG,MAAM,CAAC2B,eAAgB;cAAAzB,QAAA,GAC1CzD,IAAA,CAACJ,QAAQ;gBAACuF,IAAI,EAAC,SAAS;gBAACzB,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAM,CAAE,CAAC,EAClD3D,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC6B,UAAW;gBAAA3B,QAAA,EAAExC,KAAK,CAACJ;cAAY,CAAO,CAAC,EAC3Db,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC8B,SAAU;gBAAA5B,QAAA,EAAC;cAAa,CAAM,CAAC;YAAA,CACvC;UAAC,CACX,CAAC;QAAA,CACH,CAAC,EAEPvD,KAAA,CAACpB,IAAI;UAACsE,KAAK,EAAEG,MAAM,CAACuB,QAAS;UAAArB,QAAA,GAC3BzD,IAAA,CAACb,IAAI;YAACiE,KAAK,EAAE,CAACG,MAAM,CAACwB,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAvB,QAAA,EAC7DvD,KAAA,CAACf,IAAI,CAAC8F,OAAO;cAAC7B,KAAK,EAAEG,MAAM,CAAC2B,eAAgB;cAAAzB,QAAA,GAC1CzD,IAAA,CAACJ,QAAQ;gBAACuF,IAAI,EAAC,MAAM;gBAACzB,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAM,CAAE,CAAC,EAC/C3D,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC6B,UAAW;gBAAA3B,QAAA,EAAET,cAAc,CAAC/B,KAAK,CAACH,UAAU;cAAC,CAAO,CAAC,EACzEd,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC8B,SAAU;gBAAA5B,QAAA,EAAC;cAAW,CAAM,CAAC;YAAA,CACrC;UAAC,CACX,CAAC,EAEPzD,IAAA,CAACb,IAAI;YAACiE,KAAK,EAAE,CAACG,MAAM,CAACwB,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAvB,QAAA,EAC7DvD,KAAA,CAACf,IAAI,CAAC8F,OAAO;cAAC7B,KAAK,EAAEG,MAAM,CAAC2B,eAAgB;cAAAzB,QAAA,GAC1CzD,IAAA,CAACJ,QAAQ;gBAACuF,IAAI,EAAC,aAAa;gBAACzB,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAM,CAAE,CAAC,EACtD3D,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC6B,UAAW;gBAAA3B,QAAA,EAAET,cAAc,CAAC/B,KAAK,CAACF,WAAW;cAAC,CAAO,CAAC,EAC1Ef,IAAA,CAACR,IAAI;gBAAC4D,KAAK,EAAEG,MAAM,CAAC8B,SAAU;gBAAA5B,QAAA,EAAC;cAAY,CAAM,CAAC;YAAA,CACtC;UAAC,CACX,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAGPzD,IAAA,CAACb,IAAI;QAACiE,KAAK,EAAEG,MAAM,CAAC+B,WAAY;QAAA7B,QAAA,EAC9BvD,KAAA,CAACf,IAAI,CAAC8F,OAAO;UAAAxB,QAAA,GACXzD,IAAA,CAACZ,KAAK;YAACgE,KAAK,EAAEG,MAAM,CAACgC,YAAa;YAAA9B,QAAA,EAAC;UAAa,CAAO,CAAC,EAExDvD,KAAA,CAACpB,IAAI;YAACsE,KAAK,EAAEG,MAAM,CAACiC,gBAAiB;YAAA/B,QAAA,GACnCzD,IAAA,CAACV,MAAM;cACLmG,IAAI,EAAC,WAAW;cAChBlB,IAAI,EAAC,MAAM;cACXnB,KAAK,EAAE,CAACG,MAAM,CAACmC,YAAY,EAAE;gBAAEV,eAAe,EAAEnF,KAAK,CAAC+D,MAAM,CAAC+B;cAAU,CAAC,CAAE;cAC1EC,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQvF,UAAU,CAACwF,QAAQ,CAAC,cAAc,CAAC;cAAA,CAAC;cAAApC,QAAA,EACpD;YAED,CAAQ,CAAC,EAETzD,IAAA,CAACV,MAAM;cACLmG,IAAI,EAAC,UAAU;cACflB,IAAI,EAAC,aAAa;cAClBnB,KAAK,EAAEG,MAAM,CAACmC,YAAa;cAC3BE,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQvF,UAAU,CAACwF,QAAQ,CAAC,WAAW,CAAC;cAAA,CAAC;cAAApC,QAAA,EACjD;YAED,CAAQ,CAAC;UAAA,CACL,CAAC,EAEPvD,KAAA,CAACpB,IAAI;YAACsE,KAAK,EAAEG,MAAM,CAACiC,gBAAiB;YAAA/B,QAAA,GACnCzD,IAAA,CAACV,MAAM;cACLmG,IAAI,EAAC,UAAU;cACflB,IAAI,EAAC,cAAc;cACnBnB,KAAK,EAAEG,MAAM,CAACmC,YAAa;cAC3BE,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQvF,UAAU,CAACwF,QAAQ,CAAC,UAAU,CAAC;cAAA,CAAC;cAAApC,QAAA,EAChD;YAED,CAAQ,CAAC,EAETzD,IAAA,CAACV,MAAM;cACLmG,IAAI,EAAC,UAAU;cACflB,IAAI,EAAC,YAAY;cACjBnB,KAAK,EAAEG,MAAM,CAACmC,YAAa;cAC3BE,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQvF,UAAU,CAACwF,QAAQ,CAAC,YAAY,CAAC;cAAA,CAAC;cAAApC,QAAA,EAClD;YAED,CAAQ,CAAC;UAAA,CACL,CAAC;QAAA,CACK;MAAC,CACX,CAAC,EAGPzD,IAAA,CAACb,IAAI;QAACiE,KAAK,EAAEG,MAAM,CAACuC,QAAS;QAAArC,QAAA,EAC3BvD,KAAA,CAACf,IAAI,CAAC8F,OAAO;UAAAxB,QAAA,GACXzD,IAAA,CAACZ,KAAK;YAACgE,KAAK,EAAEG,MAAM,CAACwC,SAAU;YAAAtC,QAAA,EAAC;UAAO,CAAO,CAAC,EAC/CvD,KAAA,CAACb,SAAS;YAAC+D,KAAK,EAAEG,MAAM,CAACyC,QAAS;YAAAvC,QAAA,GAAC,kBACpB,EAAC,IAAI,EAAC,+BACC,EAAC,IAAIY,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;UAAA,CAClD,CAAC;QAAA,CACA;MAAC,CACX,CAAC;IAAA,CACG,CAAC,EAEbtE,IAAA,CAACT,GAAG;MACF6D,KAAK,EAAEG,MAAM,CAAC0C,GAAI;MAClB1B,IAAI,EAAC,MAAM;MACXqB,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQvF,UAAU,CAACwF,QAAQ,CAAC,cAAc,CAAC;MAAA;IAAC,CACpD,CAAC;EAAA,CACE,CAAC;AAEX;AAEA,IAAMtC,MAAM,GAAGxE,UAAU,CAACmH,MAAM,CAAC;EAC/BnC,SAAS,EAAE;IACToC,IAAI,EAAE,CAAC;IACPnB,eAAe,EAAEnF,KAAK,CAAC+D,MAAM,CAACwC;EAChC,CAAC;EACD5C,gBAAgB,EAAE;IAChB2C,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDxC,WAAW,EAAE;IACXyC,SAAS,EAAE,EAAE;IACb5C,KAAK,EAAE9D,KAAK,CAAC+D,MAAM,CAAC4C;EACtB,CAAC;EACDxC,MAAM,EAAE;IACNyC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE;EACrB,CAAC;EACD1C,aAAa,EAAE;IACbqC,UAAU,EAAE;EACd,CAAC;EACDpC,WAAW,EAAE;IACXP,KAAK,EAAE,MAAM;IACbiD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDzC,cAAc,EAAE;IACdT,KAAK,EAAE,SAAS;IAChBiD,QAAQ,EAAE,EAAE;IACZL,SAAS,EAAE;EACb,CAAC;EACD/B,UAAU,EAAE;IACV+B,SAAS,EAAE,EAAE;IACbvB,eAAe,EAAE;EACnB,CAAC;EACDN,cAAc,EAAE;IACdf,KAAK,EAAE;EACT,CAAC;EACDgB,OAAO,EAAE;IACPwB,IAAI,EAAE,CAAC;IACPW,OAAO,EAAE;EACX,CAAC;EACDjC,cAAc,EAAE;IACdkC,YAAY,EAAE;EAChB,CAAC;EACDjC,QAAQ,EAAE;IACRkC,aAAa,EAAE,KAAK;IACpBX,cAAc,EAAE,eAAe;IAC/BU,YAAY,EAAE;EAChB,CAAC;EACDhC,QAAQ,EAAE;IACRoB,IAAI,EAAE,CAAC;IACPc,gBAAgB,EAAE,CAAC;IACnBC,SAAS,EAAE;EACb,CAAC;EACDhC,eAAe,EAAE;IACfoB,UAAU,EAAE,QAAQ;IACpBa,eAAe,EAAE;EACnB,CAAC;EACD/B,UAAU,EAAE;IACVwB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBlD,KAAK,EAAE,MAAM;IACb4C,SAAS,EAAE;EACb,CAAC;EACDlB,SAAS,EAAE;IACTuB,QAAQ,EAAE,EAAE;IACZjD,KAAK,EAAE,MAAM;IACb4C,SAAS,EAAE;EACb,CAAC;EACDjB,WAAW,EAAE;IACXyB,YAAY,EAAE,EAAE;IAChBG,SAAS,EAAE;EACb,CAAC;EACD3B,YAAY,EAAE;IACZwB,YAAY,EAAE,EAAE;IAChBpD,KAAK,EAAE9D,KAAK,CAAC+D,MAAM,CAACC;EACtB,CAAC;EACD2B,gBAAgB,EAAE;IAChBwB,aAAa,EAAE,KAAK;IACpBX,cAAc,EAAE,eAAe;IAC/BU,YAAY,EAAE;EAChB,CAAC;EACDrB,YAAY,EAAE;IACZS,IAAI,EAAE,CAAC;IACPc,gBAAgB,EAAE;EACpB,CAAC;EACDnB,QAAQ,EAAE;IACRiB,YAAY,EAAE,EAAE;IAChBG,SAAS,EAAE;EACb,CAAC;EACDnB,SAAS,EAAE;IACTa,QAAQ,EAAE,EAAE;IACZjD,KAAK,EAAE9D,KAAK,CAAC+D,MAAM,CAACC;EACtB,CAAC;EACDmC,QAAQ,EAAE;IACRrC,KAAK,EAAE9D,KAAK,CAAC+D,MAAM,CAACwD,SAAS;IAC7Bb,SAAS,EAAE;EACb,CAAC;EACDN,GAAG,EAAE;IACHoB,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTxC,eAAe,EAAEnF,KAAK,CAAC+D,MAAM,CAAC+B;EAChC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}