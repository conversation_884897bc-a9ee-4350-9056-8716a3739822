{"ast": null, "code": "import ReactNativePlatform from \"react-native-web/dist/exports/Platform\";\nimport { isDOMAvailable, canUseEventListeners, canUseViewport, isAsyncDebugging } from \"./environment/browser\";\nvar Platform = {\n  OS: ReactNativePlatform.OS,\n  select: ReactNativePlatform.select,\n  isDOMAvailable: isDOMAvailable,\n  canUseEventListeners: canUseEventListeners,\n  canUseViewport: canUseViewport,\n  isAsyncDebugging: isAsyncDebugging\n};\nexport default Platform;", "map": {"version": 3, "names": ["isDOMAvailable", "canUseEventListeners", "canUseViewport", "isAsyncDebugging", "Platform", "OS", "ReactNativePlatform", "select"], "sources": ["C:\\laragon\\www\\mobile-app\\node_modules\\expo-modules-core\\src\\Platform.ts"], "sourcesContent": ["import { Platform as ReactNativePlatform, PlatformOSType } from 'react-native';\n\nimport {\n  isDOMAvailable,\n  canUseEventListeners,\n  canUseViewport,\n  isAsyncDebugging,\n} from './environment/browser';\n\nexport type PlatformSelectOSType = PlatformOSType | 'native' | 'electron' | 'default';\n\nexport type PlatformSelect = <T>(specifics: { [platform in PlatformSelectOSType]?: T }) => T;\n\nconst Platform = {\n  /**\n   * Denotes the currently running platform.\n   * Can be one of ios, android, web.\n   */\n  OS: ReactNativePlatform.OS,\n  /**\n   * Returns the value with the matching platform.\n   * Object keys can be any of ios, android, native, web, default.\n   *\n   * @ios ios, native, default\n   * @android android, native, default\n   * @web web, default\n   */\n  select: ReactNativePlatform.select as PlatformSelect,\n  /**\n   * Denotes if the DOM API is available in the current environment.\n   * The DOM is not available in native React runtimes and Node.js.\n   */\n  isDOMAvailable,\n  /**\n   * Denotes if the current environment can attach event listeners\n   * to the window. This will return false in native React\n   * runtimes and Node.js.\n   */\n  canUseEventListeners,\n  /**\n   * Denotes if the current environment can inspect properties of the\n   * screen on which the current window is being rendered. This will\n   * return false in native React runtimes and Node.js.\n   */\n  canUseViewport,\n  /**\n   * If the JavaScript is being executed in a remote JavaScript environment.\n   * When `true`, synchronous native invocations cannot be executed.\n   */\n  isAsyncDebugging,\n};\n\nexport default Platform;\n"], "mappings": ";AAEA,SACEA,cAAc,EACdC,oBAAoB,EACpBC,cAAc,EACdC,gBAAgB;AAOlB,IAAMC,QAAQ,GAAG;EAKfC,EAAE,EAAEC,mBAAmB,CAACD,EAAE;EAS1BE,MAAM,EAAED,mBAAmB,CAACC,MAAwB;EAKpDP,cAAc,EAAdA,cAAc;EAMdC,oBAAoB,EAApBA,oBAAoB;EAMpBC,cAAc,EAAdA,cAAc;EAKdC,gBAAgB,EAAhBA;CACD;AAED,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}