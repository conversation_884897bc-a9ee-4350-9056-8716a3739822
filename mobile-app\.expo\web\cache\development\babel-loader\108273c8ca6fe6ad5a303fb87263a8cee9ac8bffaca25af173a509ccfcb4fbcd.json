{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport * as SecureStore from 'expo-secure-store';\nvar API_BASE_URL = 'http://*************:3000';\nvar ApiService = function () {\n  function ApiService() {\n    _classCallCheck(this, ApiService);\n  }\n  return _createClass(ApiService, [{\n    key: \"getAuthHeaders\",\n    value: function () {\n      var _getAuthHeaders = _asyncToGenerator(function* () {\n        var token = yield SecureStore.getItemAsync('userToken');\n        return {\n          'Content-Type': 'application/json',\n          'Authorization': token ? `Bearer ${token}` : ''\n        };\n      });\n      function getAuthHeaders() {\n        return _getAuthHeaders.apply(this, arguments);\n      }\n      return getAuthHeaders;\n    }()\n  }, {\n    key: \"login\",\n    value: function () {\n      var _login = _asyncToGenerator(function* (email, senha) {\n        try {\n          var response = yield fetch(`${API_BASE_URL}/api/auth/login`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              email: email,\n              senha: senha\n            })\n          });\n          var data = yield response.json();\n          if (response.ok && data.success) {\n            yield SecureStore.setItemAsync('userToken', data.token);\n            yield SecureStore.setItemAsync('userData', JSON.stringify(data.user));\n            return {\n              success: true,\n              data: {\n                user: data.user,\n                token: data.token\n              }\n            };\n          } else {\n            return {\n              success: false,\n              error: data.message || 'Erro no login'\n            };\n          }\n        } catch (error) {\n          return {\n            success: false,\n            error: 'Erro de conexão com o servidor'\n          };\n        }\n      });\n      function login(_x, _x2) {\n        return _login.apply(this, arguments);\n      }\n      return login;\n    }()\n  }, {\n    key: \"logout\",\n    value: function () {\n      var _logout = _asyncToGenerator(function* () {\n        yield SecureStore.deleteItemAsync('userToken');\n        yield SecureStore.deleteItemAsync('userData');\n      });\n      function logout() {\n        return _logout.apply(this, arguments);\n      }\n      return logout;\n    }()\n  }, {\n    key: \"getCurrentUser\",\n    value: function () {\n      var _getCurrentUser = _asyncToGenerator(function* () {\n        try {\n          var userData = yield SecureStore.getItemAsync('userData');\n          return userData ? JSON.parse(userData) : null;\n        } catch (error) {\n          return null;\n        }\n      });\n      function getCurrentUser() {\n        return _getCurrentUser.apply(this, arguments);\n      }\n      return getCurrentUser;\n    }()\n  }, {\n    key: \"getUsers\",\n    value: function () {\n      var _getUsers = _asyncToGenerator(function* (cambistaId) {\n        try {\n          var headers = yield this.getAuthHeaders();\n          var response = yield fetch(`${API_BASE_URL}/api/cambista/usuarios?cambista_id=${cambistaId}`, {\n            headers: headers\n          });\n          var data = yield response.json();\n          if (response.ok) {\n            return {\n              success: true,\n              data: data.usuarios || []\n            };\n          } else {\n            return {\n              success: false,\n              error: data.message || 'Erro ao buscar usuários'\n            };\n          }\n        } catch (error) {\n          return {\n            success: false,\n            error: 'Erro de conexão'\n          };\n        }\n      });\n      function getUsers(_x3) {\n        return _getUsers.apply(this, arguments);\n      }\n      return getUsers;\n    }()\n  }, {\n    key: \"getBilhetes\",\n    value: function () {\n      var _getBilhetes = _asyncToGenerator(function* (cambistaId) {\n        try {\n          var headers = yield this.getAuthHeaders();\n          var response = yield fetch(`${API_BASE_URL}/api/cambista/bilhetes?cambista_id=${cambistaId}`, {\n            headers: headers\n          });\n          var data = yield response.json();\n          if (response.ok) {\n            return {\n              success: true,\n              data: data.bilhetes || []\n            };\n          } else {\n            return {\n              success: false,\n              error: data.message || 'Erro ao buscar bilhetes'\n            };\n          }\n        } catch (error) {\n          return {\n            success: false,\n            error: 'Erro de conexão'\n          };\n        }\n      });\n      function getBilhetes(_x4) {\n        return _getBilhetes.apply(this, arguments);\n      }\n      return getBilhetes;\n    }()\n  }, {\n    key: \"createBilhete\",\n    value: function () {\n      var _createBilhete = _asyncToGenerator(function* (bilheteData) {\n        try {\n          var headers = yield this.getAuthHeaders();\n          var response = yield fetch(`${API_BASE_URL}/api/bilhetes`, {\n            method: 'POST',\n            headers: headers,\n            body: JSON.stringify(bilheteData)\n          });\n          var data = yield response.json();\n          if (response.ok) {\n            return {\n              success: true,\n              data: data.bilhete\n            };\n          } else {\n            return {\n              success: false,\n              error: data.message || 'Erro ao criar bilhete'\n            };\n          }\n        } catch (error) {\n          return {\n            success: false,\n            error: 'Erro de conexão'\n          };\n        }\n      });\n      function createBilhete(_x5) {\n        return _createBilhete.apply(this, arguments);\n      }\n      return createBilhete;\n    }()\n  }, {\n    key: \"updateUserBalance\",\n    value: function () {\n      var _updateUserBalance = _asyncToGenerator(function* (userId, valor, tipo) {\n        try {\n          var headers = yield this.getAuthHeaders();\n          var response = yield fetch(`${API_BASE_URL}/api/usuarios/${userId}/saldo`, {\n            method: 'PUT',\n            headers: headers,\n            body: JSON.stringify({\n              valor: valor,\n              tipo: tipo\n            })\n          });\n          var data = yield response.json();\n          if (response.ok) {\n            return {\n              success: true,\n              data: data\n            };\n          } else {\n            return {\n              success: false,\n              error: data.message || 'Erro ao atualizar saldo'\n            };\n          }\n        } catch (error) {\n          return {\n            success: false,\n            error: 'Erro de conexão'\n          };\n        }\n      });\n      function updateUserBalance(_x6, _x7, _x8) {\n        return _updateUserBalance.apply(this, arguments);\n      }\n      return updateUserBalance;\n    }()\n  }, {\n    key: \"getRelatorios\",\n    value: function () {\n      var _getRelatorios = _asyncToGenerator(function* (cambistaId, periodo) {\n        try {\n          var headers = yield this.getAuthHeaders();\n          var response = yield fetch(`${API_BASE_URL}/api/cambista/relatorios?cambista_id=${cambistaId}&periodo=${periodo}`, {\n            headers: headers\n          });\n          var data = yield response.json();\n          if (response.ok) {\n            return {\n              success: true,\n              data: data\n            };\n          } else {\n            return {\n              success: false,\n              error: data.message || 'Erro ao buscar relatórios'\n            };\n          }\n        } catch (error) {\n          return {\n            success: false,\n            error: 'Erro de conexão'\n          };\n        }\n      });\n      function getRelatorios(_x9, _x0) {\n        return _getRelatorios.apply(this, arguments);\n      }\n      return getRelatorios;\n    }()\n  }]);\n}();\nexport var apiService = new ApiService();", "map": {"version": 3, "names": ["SecureStore", "API_BASE_URL", "ApiService", "_classCallCheck", "_createClass", "key", "value", "_getAuthHeaders", "_asyncToGenerator", "token", "getItemAsync", "getAuthHeaders", "apply", "arguments", "_login", "email", "<PERSON><PERSON>a", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "success", "setItemAsync", "user", "error", "message", "login", "_x", "_x2", "_logout", "deleteItemAsync", "logout", "_getCurrent<PERSON>ser", "userData", "parse", "getCurrentUser", "_getUsers", "cambistaId", "usuarios", "getUsers", "_x3", "_getBilhetes", "bilhetes", "getBilhetes", "_x4", "_createBilhete", "bilheteData", "bilhete", "createBilhete", "_x5", "_updateUserBalance", "userId", "valor", "tipo", "updateUserBalance", "_x6", "_x7", "_x8", "_getRelatorios", "periodo", "getRelatorios", "_x9", "_x0", "apiService"], "sources": ["C:/laragon/www/mobile-app/src/services/api.ts"], "sourcesContent": ["import * as SecureStore from 'expo-secure-store';\n\n// Configuração da API\nconst API_BASE_URL = 'http://*************:3000'; // Altere para o IP do seu servidor\n\nexport interface User {\n  id: number;\n  nome: string;\n  email: string;\n  tipo: string;\n  status: string;\n  saldo: number;\n  gerente_id?: number;\n  cambista_id?: number;\n}\n\nexport interface Bilhete {\n  id: number;\n  codigo: string;\n  usuario_id: number;\n  usuario_nome: string;\n  valor_total: number;\n  quantidade_apostas: number;\n  status: string;\n  created_at: string;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nclass ApiService {\n  private async getAuthHeaders() {\n    const token = await SecureStore.getItemAsync('userToken');\n    return {\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : '',\n    };\n  }\n\n  async login(email: string, senha: string): Promise<ApiResponse<{ user: User; token: string }>> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, senha }),\n      });\n\n      const data = await response.json();\n      \n      if (response.ok && data.success) {\n        // Salvar token e dados do usuário\n        await SecureStore.setItemAsync('userToken', data.token);\n        await SecureStore.setItemAsync('userData', JSON.stringify(data.user));\n        \n        return {\n          success: true,\n          data: { user: data.user, token: data.token }\n        };\n      } else {\n        return {\n          success: false,\n          error: data.message || 'Erro no login'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão com o servidor'\n      };\n    }\n  }\n\n  async logout(): Promise<void> {\n    await SecureStore.deleteItemAsync('userToken');\n    await SecureStore.deleteItemAsync('userData');\n  }\n\n  async getCurrentUser(): Promise<User | null> {\n    try {\n      const userData = await SecureStore.getItemAsync('userData');\n      return userData ? JSON.parse(userData) : null;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  async getUsers(cambistaId: number): Promise<ApiResponse<User[]>> {\n    try {\n      const headers = await this.getAuthHeaders();\n      const response = await fetch(`${API_BASE_URL}/api/cambista/usuarios?cambista_id=${cambistaId}`, {\n        headers,\n      });\n\n      const data = await response.json();\n      \n      if (response.ok) {\n        return {\n          success: true,\n          data: data.usuarios || []\n        };\n      } else {\n        return {\n          success: false,\n          error: data.message || 'Erro ao buscar usuários'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão'\n      };\n    }\n  }\n\n  async getBilhetes(cambistaId: number): Promise<ApiResponse<Bilhete[]>> {\n    try {\n      const headers = await this.getAuthHeaders();\n      const response = await fetch(`${API_BASE_URL}/api/cambista/bilhetes?cambista_id=${cambistaId}`, {\n        headers,\n      });\n\n      const data = await response.json();\n      \n      if (response.ok) {\n        return {\n          success: true,\n          data: data.bilhetes || []\n        };\n      } else {\n        return {\n          success: false,\n          error: data.message || 'Erro ao buscar bilhetes'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão'\n      };\n    }\n  }\n\n  async createBilhete(bilheteData: any): Promise<ApiResponse<Bilhete>> {\n    try {\n      const headers = await this.getAuthHeaders();\n      const response = await fetch(`${API_BASE_URL}/api/bilhetes`, {\n        method: 'POST',\n        headers,\n        body: JSON.stringify(bilheteData),\n      });\n\n      const data = await response.json();\n      \n      if (response.ok) {\n        return {\n          success: true,\n          data: data.bilhete\n        };\n      } else {\n        return {\n          success: false,\n          error: data.message || 'Erro ao criar bilhete'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão'\n      };\n    }\n  }\n\n  async updateUserBalance(userId: number, valor: number, tipo: 'credito' | 'debito'): Promise<ApiResponse<any>> {\n    try {\n      const headers = await this.getAuthHeaders();\n      const response = await fetch(`${API_BASE_URL}/api/usuarios/${userId}/saldo`, {\n        method: 'PUT',\n        headers,\n        body: JSON.stringify({ valor, tipo }),\n      });\n\n      const data = await response.json();\n      \n      if (response.ok) {\n        return {\n          success: true,\n          data: data\n        };\n      } else {\n        return {\n          success: false,\n          error: data.message || 'Erro ao atualizar saldo'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão'\n      };\n    }\n  }\n\n  async getRelatorios(cambistaId: number, periodo: string): Promise<ApiResponse<any>> {\n    try {\n      const headers = await this.getAuthHeaders();\n      const response = await fetch(`${API_BASE_URL}/api/cambista/relatorios?cambista_id=${cambistaId}&periodo=${periodo}`, {\n        headers,\n      });\n\n      const data = await response.json();\n      \n      if (response.ok) {\n        return {\n          success: true,\n          data: data\n        };\n      } else {\n        return {\n          success: false,\n          error: data.message || 'Erro ao buscar relatórios'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Erro de conexão'\n      };\n    }\n  }\n}\n\nexport const apiService = new ApiService();\n"], "mappings": ";;;AAAA,OAAO,KAAKA,WAAW,MAAM,mBAAmB;AAGhD,IAAMC,YAAY,GAAG,2BAA2B;AAAC,IA+B3CC,UAAU;EAAA,SAAAA,WAAA;IAAAC,eAAA,OAAAD,UAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,UAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAC,eAAA,GAAAC,iBAAA,CACd,aAA+B;QAC7B,IAAMC,KAAK,SAAST,WAAW,CAACU,YAAY,CAAC,WAAW,CAAC;QACzD,OAAO;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAED,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;QAC/C,CAAC;MACH,CAAC;MAAA,SANaE,cAAcA,CAAA;QAAA,OAAAJ,eAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdF,cAAc;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA;MAAA,IAAAQ,MAAA,GAAAN,iBAAA,CAQ5B,WAAYO,KAAa,EAAEC,KAAa,EAAuD;QAC7F,IAAI;UACF,IAAMC,QAAQ,SAASC,KAAK,CAAC,GAAGjB,YAAY,iBAAiB,EAAE;YAC7DkB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cAAER,KAAK,EAALA,KAAK;cAAEC,KAAK,EAALA;YAAM,CAAC;UACvC,CAAC,CAAC;UAEF,IAAMQ,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAElC,IAAIR,QAAQ,CAACS,EAAE,IAAIF,IAAI,CAACG,OAAO,EAAE;YAE/B,MAAM3B,WAAW,CAAC4B,YAAY,CAAC,WAAW,EAAEJ,IAAI,CAACf,KAAK,CAAC;YACvD,MAAMT,WAAW,CAAC4B,YAAY,CAAC,UAAU,EAAEN,IAAI,CAACC,SAAS,CAACC,IAAI,CAACK,IAAI,CAAC,CAAC;YAErE,OAAO;cACLF,OAAO,EAAE,IAAI;cACbH,IAAI,EAAE;gBAAEK,IAAI,EAAEL,IAAI,CAACK,IAAI;gBAAEpB,KAAK,EAAEe,IAAI,CAACf;cAAM;YAC7C,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACLkB,OAAO,EAAE,KAAK;cACdG,KAAK,EAAEN,IAAI,CAACO,OAAO,IAAI;YACzB,CAAC;UACH;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd,OAAO;YACLH,OAAO,EAAE,KAAK;YACdG,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SAjCKE,KAAKA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAApB,MAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAALmB,KAAK;IAAA;EAAA;IAAA3B,GAAA;IAAAC,KAAA;MAAA,IAAA6B,OAAA,GAAA3B,iBAAA,CAmCX,aAA8B;QAC5B,MAAMR,WAAW,CAACoC,eAAe,CAAC,WAAW,CAAC;QAC9C,MAAMpC,WAAW,CAACoC,eAAe,CAAC,UAAU,CAAC;MAC/C,CAAC;MAAA,SAHKC,MAAMA,CAAA;QAAA,OAAAF,OAAA,CAAAvB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAANwB,MAAM;IAAA;EAAA;IAAAhC,GAAA;IAAAC,KAAA;MAAA,IAAAgC,eAAA,GAAA9B,iBAAA,CAKZ,aAA6C;QAC3C,IAAI;UACF,IAAM+B,QAAQ,SAASvC,WAAW,CAACU,YAAY,CAAC,UAAU,CAAC;UAC3D,OAAO6B,QAAQ,GAAGjB,IAAI,CAACkB,KAAK,CAACD,QAAQ,CAAC,GAAG,IAAI;QAC/C,CAAC,CAAC,OAAOT,KAAK,EAAE;UACd,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SAPKW,cAAcA,CAAA;QAAA,OAAAH,eAAA,CAAA1B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAd4B,cAAc;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA;MAAA,IAAAoC,SAAA,GAAAlC,iBAAA,CASpB,WAAemC,UAAkB,EAAgC;QAC/D,IAAI;UACF,IAAMvB,OAAO,SAAS,IAAI,CAACT,cAAc,CAAC,CAAC;UAC3C,IAAMM,QAAQ,SAASC,KAAK,CAAC,GAAGjB,YAAY,sCAAsC0C,UAAU,EAAE,EAAE;YAC9FvB,OAAO,EAAPA;UACF,CAAC,CAAC;UAEF,IAAMI,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;YACf,OAAO;cACLC,OAAO,EAAE,IAAI;cACbH,IAAI,EAAEA,IAAI,CAACoB,QAAQ,IAAI;YACzB,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACLjB,OAAO,EAAE,KAAK;cACdG,KAAK,EAAEN,IAAI,CAACO,OAAO,IAAI;YACzB,CAAC;UACH;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd,OAAO;YACLH,OAAO,EAAE,KAAK;YACdG,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SA1BKe,QAAQA,CAAAC,GAAA;QAAA,OAAAJ,SAAA,CAAA9B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARgC,QAAQ;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,YAAA,GAAAvC,iBAAA,CA4Bd,WAAkBmC,UAAkB,EAAmC;QACrE,IAAI;UACF,IAAMvB,OAAO,SAAS,IAAI,CAACT,cAAc,CAAC,CAAC;UAC3C,IAAMM,QAAQ,SAASC,KAAK,CAAC,GAAGjB,YAAY,sCAAsC0C,UAAU,EAAE,EAAE;YAC9FvB,OAAO,EAAPA;UACF,CAAC,CAAC;UAEF,IAAMI,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;YACf,OAAO;cACLC,OAAO,EAAE,IAAI;cACbH,IAAI,EAAEA,IAAI,CAACwB,QAAQ,IAAI;YACzB,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACLrB,OAAO,EAAE,KAAK;cACdG,KAAK,EAAEN,IAAI,CAACO,OAAO,IAAI;YACzB,CAAC;UACH;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd,OAAO;YACLH,OAAO,EAAE,KAAK;YACdG,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SA1BKmB,WAAWA,CAAAC,GAAA;QAAA,OAAAH,YAAA,CAAAnC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXoC,WAAW;IAAA;EAAA;IAAA5C,GAAA;IAAAC,KAAA;MAAA,IAAA6C,cAAA,GAAA3C,iBAAA,CA4BjB,WAAoB4C,WAAgB,EAAiC;QACnE,IAAI;UACF,IAAMhC,OAAO,SAAS,IAAI,CAACT,cAAc,CAAC,CAAC;UAC3C,IAAMM,QAAQ,SAASC,KAAK,CAAC,GAAGjB,YAAY,eAAe,EAAE;YAC3DkB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAPA,OAAO;YACPC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC6B,WAAW;UAClC,CAAC,CAAC;UAEF,IAAM5B,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;YACf,OAAO;cACLC,OAAO,EAAE,IAAI;cACbH,IAAI,EAAEA,IAAI,CAAC6B;YACb,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACL1B,OAAO,EAAE,KAAK;cACdG,KAAK,EAAEN,IAAI,CAACO,OAAO,IAAI;YACzB,CAAC;UACH;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd,OAAO;YACLH,OAAO,EAAE,KAAK;YACdG,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SA5BKwB,aAAaA,CAAAC,GAAA;QAAA,OAAAJ,cAAA,CAAAvC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbyC,aAAa;IAAA;EAAA;IAAAjD,GAAA;IAAAC,KAAA;MAAA,IAAAkD,kBAAA,GAAAhD,iBAAA,CA8BnB,WAAwBiD,MAAc,EAAEC,KAAa,EAAEC,IAA0B,EAA6B;QAC5G,IAAI;UACF,IAAMvC,OAAO,SAAS,IAAI,CAACT,cAAc,CAAC,CAAC;UAC3C,IAAMM,QAAQ,SAASC,KAAK,CAAC,GAAGjB,YAAY,iBAAiBwD,MAAM,QAAQ,EAAE;YAC3EtC,MAAM,EAAE,KAAK;YACbC,OAAO,EAAPA,OAAO;YACPC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cAAEmC,KAAK,EAALA,KAAK;cAAEC,IAAI,EAAJA;YAAK,CAAC;UACtC,CAAC,CAAC;UAEF,IAAMnC,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;YACf,OAAO;cACLC,OAAO,EAAE,IAAI;cACbH,IAAI,EAAEA;YACR,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACLG,OAAO,EAAE,KAAK;cACdG,KAAK,EAAEN,IAAI,CAACO,OAAO,IAAI;YACzB,CAAC;UACH;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd,OAAO;YACLH,OAAO,EAAE,KAAK;YACdG,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SA5BK8B,iBAAiBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAP,kBAAA,CAAA5C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB+C,iBAAiB;IAAA;EAAA;IAAAvD,GAAA;IAAAC,KAAA;MAAA,IAAA0D,cAAA,GAAAxD,iBAAA,CA8BvB,WAAoBmC,UAAkB,EAAEsB,OAAe,EAA6B;QAClF,IAAI;UACF,IAAM7C,OAAO,SAAS,IAAI,CAACT,cAAc,CAAC,CAAC;UAC3C,IAAMM,QAAQ,SAASC,KAAK,CAAC,GAAGjB,YAAY,wCAAwC0C,UAAU,YAAYsB,OAAO,EAAE,EAAE;YACnH7C,OAAO,EAAPA;UACF,CAAC,CAAC;UAEF,IAAMI,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;YACf,OAAO;cACLC,OAAO,EAAE,IAAI;cACbH,IAAI,EAAEA;YACR,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACLG,OAAO,EAAE,KAAK;cACdG,KAAK,EAAEN,IAAI,CAACO,OAAO,IAAI;YACzB,CAAC;UACH;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd,OAAO;YACLH,OAAO,EAAE,KAAK;YACdG,KAAK,EAAE;UACT,CAAC;QACH;MACF,CAAC;MAAA,SA1BKoC,aAAaA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,cAAA,CAAApD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbqD,aAAa;IAAA;EAAA;AAAA;AA6BrB,OAAO,IAAMG,UAAU,GAAG,IAAInE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}