{"ast": null, "code": "import isSelectionValid from \"../../modules/isSelectionValid\";\nvar keyName = '__reactResponderId';\nfunction getEventPath(domEvent) {\n  if (domEvent.type === 'selectionchange') {\n    var target = window.getSelection().anchorNode;\n    return composedPathFallback(target);\n  } else {\n    var path = domEvent.composedPath != null ? domEvent.composedPath() : composedPathFallback(domEvent.target);\n    return path;\n  }\n}\nfunction composedPathFallback(target) {\n  var path = [];\n  while (target != null && target !== document.body) {\n    path.push(target);\n    target = target.parentNode;\n  }\n  return path;\n}\nfunction getResponderId(node) {\n  if (node != null) {\n    return node[keyName];\n  }\n  return null;\n}\nexport function setResponderId(node, id) {\n  if (node != null) {\n    node[keyName] = id;\n  }\n}\nexport function getResponderPaths(domEvent) {\n  var idPath = [];\n  var nodePath = [];\n  var eventPath = getEventPath(domEvent);\n  for (var i = 0; i < eventPath.length; i++) {\n    var node = eventPath[i];\n    var id = getResponderId(node);\n    if (id != null) {\n      idPath.push(id);\n      nodePath.push(node);\n    }\n  }\n  return {\n    idPath: idPath,\n    nodePath: nodePath\n  };\n}\nexport function getLowestCommonAncestor(pathA, pathB) {\n  var pathALength = pathA.length;\n  var pathBLength = pathB.length;\n  if (pathALength === 0 || pathBLength === 0 || pathA[pathALength - 1] !== pathB[pathBLength - 1]) {\n    return null;\n  }\n  var itemA = pathA[0];\n  var indexA = 0;\n  var itemB = pathB[0];\n  var indexB = 0;\n  if (pathALength - pathBLength > 0) {\n    indexA = pathALength - pathBLength;\n    itemA = pathA[indexA];\n    pathALength = pathBLength;\n  }\n  if (pathBLength - pathALength > 0) {\n    indexB = pathBLength - pathALength;\n    itemB = pathB[indexB];\n    pathBLength = pathALength;\n  }\n  var depth = pathALength;\n  while (depth--) {\n    if (itemA === itemB) {\n      return itemA;\n    }\n    itemA = pathA[indexA++];\n    itemB = pathB[indexB++];\n  }\n  return null;\n}\nexport function hasTargetTouches(target, touches) {\n  if (!touches || touches.length === 0) {\n    return false;\n  }\n  for (var i = 0; i < touches.length; i++) {\n    var node = touches[i].target;\n    if (node != null) {\n      if (target.contains(node)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\nexport function hasValidSelection(domEvent) {\n  if (domEvent.type === 'selectionchange') {\n    return isSelectionValid();\n  }\n  return domEvent.type === 'select';\n}\nexport function isPrimaryPointerDown(domEvent) {\n  var altKey = domEvent.altKey,\n    button = domEvent.button,\n    buttons = domEvent.buttons,\n    ctrlKey = domEvent.ctrlKey,\n    type = domEvent.type;\n  var isTouch = type === 'touchstart' || type === 'touchmove';\n  var isPrimaryMouseDown = type === 'mousedown' && (button === 0 || buttons === 1);\n  var isPrimaryMouseMove = type === 'mousemove' && buttons === 1;\n  var noModifiers = altKey === false && ctrlKey === false;\n  if (isTouch || isPrimaryMouseDown && noModifiers || isPrimaryMouseMove && noModifiers) {\n    return true;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["isSelectionValid", "keyName", "getEventPath", "domEvent", "type", "target", "window", "getSelection", "anchorNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "<PERSON><PERSON><PERSON>", "document", "body", "push", "parentNode", "getResponderId", "node", "setResponderId", "id", "getResponderPaths", "idPath", "nodePath", "eventPath", "i", "length", "getLowestCommonAncestor", "pathA", "pathB", "pathALength", "path<PERSON>ength", "itemA", "indexA", "itemB", "indexB", "depth", "hasTargetTouches", "touches", "contains", "hasValidSelection", "isPrimaryPointerDown", "altKey", "button", "buttons", "ctrl<PERSON>ey", "is<PERSON><PERSON>ch", "isPrimaryMouseDown", "isPrimaryMouseMove", "noModifiers"], "sources": ["C:/laragon/www/mobile-app/node_modules/react-native-web/dist/modules/useResponderEvents/utils.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport isSelectionValid from '../../modules/isSelectionValid';\nvar keyName = '__reactResponderId';\nfunction getEventPath(domEvent) {\n  // The 'selectionchange' event always has the 'document' as the target.\n  // Use the anchor node as the initial target to reconstruct a path.\n  // (We actually only need the first \"responder\" node in practice.)\n  if (domEvent.type === 'selectionchange') {\n    var target = window.getSelection().anchorNode;\n    return composedPathFallback(target);\n  } else {\n    var path = domEvent.composedPath != null ? domEvent.composedPath() : composedPathFallback(domEvent.target);\n    return path;\n  }\n}\nfunction composedPathFallback(target) {\n  var path = [];\n  while (target != null && target !== document.body) {\n    path.push(target);\n    target = target.parentNode;\n  }\n  return path;\n}\n\n/**\n * Retrieve the responderId from a host node\n */\nfunction getResponderId(node) {\n  if (node != null) {\n    return node[keyName];\n  }\n  return null;\n}\n\n/**\n * Store the responderId on a host node\n */\nexport function setResponderId(node, id) {\n  if (node != null) {\n    node[keyName] = id;\n  }\n}\n\n/**\n * Filter the event path to contain only the nodes attached to the responder system\n */\nexport function getResponderPaths(domEvent) {\n  var idPath = [];\n  var nodePath = [];\n  var eventPath = getEventPath(domEvent);\n  for (var i = 0; i < eventPath.length; i++) {\n    var node = eventPath[i];\n    var id = getResponderId(node);\n    if (id != null) {\n      idPath.push(id);\n      nodePath.push(node);\n    }\n  }\n  return {\n    idPath,\n    nodePath\n  };\n}\n\n/**\n * Walk the paths and find the first common ancestor\n */\nexport function getLowestCommonAncestor(pathA, pathB) {\n  var pathALength = pathA.length;\n  var pathBLength = pathB.length;\n  if (\n  // If either path is empty\n  pathALength === 0 || pathBLength === 0 ||\n  // If the last elements aren't the same there can't be a common ancestor\n  // that is connected to the responder system\n  pathA[pathALength - 1] !== pathB[pathBLength - 1]) {\n    return null;\n  }\n  var itemA = pathA[0];\n  var indexA = 0;\n  var itemB = pathB[0];\n  var indexB = 0;\n\n  // If A is deeper, skip indices that can't match.\n  if (pathALength - pathBLength > 0) {\n    indexA = pathALength - pathBLength;\n    itemA = pathA[indexA];\n    pathALength = pathBLength;\n  }\n\n  // If B is deeper, skip indices that can't match\n  if (pathBLength - pathALength > 0) {\n    indexB = pathBLength - pathALength;\n    itemB = pathB[indexB];\n    pathBLength = pathALength;\n  }\n\n  // Walk in lockstep until a match is found\n  var depth = pathALength;\n  while (depth--) {\n    if (itemA === itemB) {\n      return itemA;\n    }\n    itemA = pathA[indexA++];\n    itemB = pathB[indexB++];\n  }\n  return null;\n}\n\n/**\n * Determine whether any of the active touches are within the current responder.\n * This cannot rely on W3C `targetTouches`, as neither IE11 nor Safari implement it.\n */\nexport function hasTargetTouches(target, touches) {\n  if (!touches || touches.length === 0) {\n    return false;\n  }\n  for (var i = 0; i < touches.length; i++) {\n    var node = touches[i].target;\n    if (node != null) {\n      if (target.contains(node)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\n/**\n * Ignore 'selectionchange' events that don't correspond with a person's intent to\n * select text.\n */\nexport function hasValidSelection(domEvent) {\n  if (domEvent.type === 'selectionchange') {\n    return isSelectionValid();\n  }\n  return domEvent.type === 'select';\n}\n\n/**\n * Events are only valid if the primary button was used without specific modifier keys.\n */\nexport function isPrimaryPointerDown(domEvent) {\n  var altKey = domEvent.altKey,\n    button = domEvent.button,\n    buttons = domEvent.buttons,\n    ctrlKey = domEvent.ctrlKey,\n    type = domEvent.type;\n  var isTouch = type === 'touchstart' || type === 'touchmove';\n  var isPrimaryMouseDown = type === 'mousedown' && (button === 0 || buttons === 1);\n  var isPrimaryMouseMove = type === 'mousemove' && buttons === 1;\n  var noModifiers = altKey === false && ctrlKey === false;\n  if (isTouch || isPrimaryMouseDown && noModifiers || isPrimaryMouseMove && noModifiers) {\n    return true;\n  }\n  return false;\n}"], "mappings": "AASA,OAAOA,gBAAgB;AACvB,IAAIC,OAAO,GAAG,oBAAoB;AAClC,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAI9B,IAAIA,QAAQ,CAACC,IAAI,KAAK,iBAAiB,EAAE;IACvC,IAAIC,MAAM,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CAACC,UAAU;IAC7C,OAAOC,oBAAoB,CAACJ,MAAM,CAAC;EACrC,CAAC,MAAM;IACL,IAAIK,IAAI,GAAGP,QAAQ,CAACQ,YAAY,IAAI,IAAI,GAAGR,QAAQ,CAACQ,YAAY,CAAC,CAAC,GAAGF,oBAAoB,CAACN,QAAQ,CAACE,MAAM,CAAC;IAC1G,OAAOK,IAAI;EACb;AACF;AACA,SAASD,oBAAoBA,CAACJ,MAAM,EAAE;EACpC,IAAIK,IAAI,GAAG,EAAE;EACb,OAAOL,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAKO,QAAQ,CAACC,IAAI,EAAE;IACjDH,IAAI,CAACI,IAAI,CAACT,MAAM,CAAC;IACjBA,MAAM,GAAGA,MAAM,CAACU,UAAU;EAC5B;EACA,OAAOL,IAAI;AACb;AAKA,SAASM,cAAcA,CAACC,IAAI,EAAE;EAC5B,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOA,IAAI,CAAChB,OAAO,CAAC;EACtB;EACA,OAAO,IAAI;AACb;AAKA,OAAO,SAASiB,cAAcA,CAACD,IAAI,EAAEE,EAAE,EAAE;EACvC,IAAIF,IAAI,IAAI,IAAI,EAAE;IAChBA,IAAI,CAAChB,OAAO,CAAC,GAAGkB,EAAE;EACpB;AACF;AAKA,OAAO,SAASC,iBAAiBA,CAACjB,QAAQ,EAAE;EAC1C,IAAIkB,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,SAAS,GAAGrB,YAAY,CAACC,QAAQ,CAAC;EACtC,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,IAAIP,IAAI,GAAGM,SAAS,CAACC,CAAC,CAAC;IACvB,IAAIL,EAAE,GAAGH,cAAc,CAACC,IAAI,CAAC;IAC7B,IAAIE,EAAE,IAAI,IAAI,EAAE;MACdE,MAAM,CAACP,IAAI,CAACK,EAAE,CAAC;MACfG,QAAQ,CAACR,IAAI,CAACG,IAAI,CAAC;IACrB;EACF;EACA,OAAO;IACLI,MAAM,EAANA,MAAM;IACNC,QAAQ,EAARA;EACF,CAAC;AACH;AAKA,OAAO,SAASI,uBAAuBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACpD,IAAIC,WAAW,GAAGF,KAAK,CAACF,MAAM;EAC9B,IAAIK,WAAW,GAAGF,KAAK,CAACH,MAAM;EAC9B,IAEAI,WAAW,KAAK,CAAC,IAAIC,WAAW,KAAK,CAAC,IAGtCH,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,KAAKD,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE;IACjD,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACpB,IAAIK,MAAM,GAAG,CAAC;EACd,IAAIC,KAAK,GAAGL,KAAK,CAAC,CAAC,CAAC;EACpB,IAAIM,MAAM,GAAG,CAAC;EAGd,IAAIL,WAAW,GAAGC,WAAW,GAAG,CAAC,EAAE;IACjCE,MAAM,GAAGH,WAAW,GAAGC,WAAW;IAClCC,KAAK,GAAGJ,KAAK,CAACK,MAAM,CAAC;IACrBH,WAAW,GAAGC,WAAW;EAC3B;EAGA,IAAIA,WAAW,GAAGD,WAAW,GAAG,CAAC,EAAE;IACjCK,MAAM,GAAGJ,WAAW,GAAGD,WAAW;IAClCI,KAAK,GAAGL,KAAK,CAACM,MAAM,CAAC;IACrBJ,WAAW,GAAGD,WAAW;EAC3B;EAGA,IAAIM,KAAK,GAAGN,WAAW;EACvB,OAAOM,KAAK,EAAE,EAAE;IACd,IAAIJ,KAAK,KAAKE,KAAK,EAAE;MACnB,OAAOF,KAAK;IACd;IACAA,KAAK,GAAGJ,KAAK,CAACK,MAAM,EAAE,CAAC;IACvBC,KAAK,GAAGL,KAAK,CAACM,MAAM,EAAE,CAAC;EACzB;EACA,OAAO,IAAI;AACb;AAMA,OAAO,SAASE,gBAAgBA,CAAC/B,MAAM,EAAEgC,OAAO,EAAE;EAChD,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACZ,MAAM,KAAK,CAAC,EAAE;IACpC,OAAO,KAAK;EACd;EACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,OAAO,CAACZ,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIP,IAAI,GAAGoB,OAAO,CAACb,CAAC,CAAC,CAACnB,MAAM;IAC5B,IAAIY,IAAI,IAAI,IAAI,EAAE;MAChB,IAAIZ,MAAM,CAACiC,QAAQ,CAACrB,IAAI,CAAC,EAAE;QACzB,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd;AAMA,OAAO,SAASsB,iBAAiBA,CAACpC,QAAQ,EAAE;EAC1C,IAAIA,QAAQ,CAACC,IAAI,KAAK,iBAAiB,EAAE;IACvC,OAAOJ,gBAAgB,CAAC,CAAC;EAC3B;EACA,OAAOG,QAAQ,CAACC,IAAI,KAAK,QAAQ;AACnC;AAKA,OAAO,SAASoC,oBAAoBA,CAACrC,QAAQ,EAAE;EAC7C,IAAIsC,MAAM,GAAGtC,QAAQ,CAACsC,MAAM;IAC1BC,MAAM,GAAGvC,QAAQ,CAACuC,MAAM;IACxBC,OAAO,GAAGxC,QAAQ,CAACwC,OAAO;IAC1BC,OAAO,GAAGzC,QAAQ,CAACyC,OAAO;IAC1BxC,IAAI,GAAGD,QAAQ,CAACC,IAAI;EACtB,IAAIyC,OAAO,GAAGzC,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW;EAC3D,IAAI0C,kBAAkB,GAAG1C,IAAI,KAAK,WAAW,KAAKsC,MAAM,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,CAAC;EAChF,IAAII,kBAAkB,GAAG3C,IAAI,KAAK,WAAW,IAAIuC,OAAO,KAAK,CAAC;EAC9D,IAAIK,WAAW,GAAGP,MAAM,KAAK,KAAK,IAAIG,OAAO,KAAK,KAAK;EACvD,IAAIC,OAAO,IAAIC,kBAAkB,IAAIE,WAAW,IAAID,kBAAkB,IAAIC,WAAW,EAAE;IACrF,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}