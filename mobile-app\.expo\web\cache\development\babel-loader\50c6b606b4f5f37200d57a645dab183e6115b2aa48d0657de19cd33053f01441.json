{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { DefaultTheme } from 'react-native-paper';\nexport var theme = _objectSpread(_objectSpread({}, DefaultTheme), {}, {\n  colors: _objectSpread(_objectSpread({}, DefaultTheme.colors), {}, {\n    primary: '#1e40af',\n    secondary: '#059669',\n    accent: '#f59e0b',\n    background: '#f8fafc',\n    surface: '#ffffff',\n    text: '#1f2937',\n    onSurface: '#374151',\n    disabled: '#9ca3af',\n    placeholder: '#6b7280',\n    backdrop: 'rgba(0, 0, 0, 0.5)',\n    error: '#dc2626',\n    success: '#059669',\n    warning: '#f59e0b',\n    info: '#3b82f6'\n  }),\n  fonts: _objectSpread(_objectSpread({}, DefaultTheme.fonts), {}, {\n    regular: {\n      fontFamily: 'System',\n      fontWeight: '400'\n    },\n    medium: {\n      fontFamily: 'System',\n      fontWeight: '500'\n    },\n    light: {\n      fontFamily: 'System',\n      fontWeight: '300'\n    },\n    thin: {\n      fontFamily: 'System',\n      fontWeight: '100'\n    }\n  }),\n  roundness: 8\n});", "map": {"version": 3, "names": ["DefaultTheme", "theme", "_objectSpread", "colors", "primary", "secondary", "accent", "background", "surface", "text", "onSurface", "disabled", "placeholder", "backdrop", "error", "success", "warning", "info", "fonts", "regular", "fontFamily", "fontWeight", "medium", "light", "thin", "roundness"], "sources": ["C:/laragon/www/mobile-app/src/theme/theme.ts"], "sourcesContent": ["import { DefaultTheme } from 'react-native-paper';\n\nexport const theme = {\n  ...DefaultTheme,\n  colors: {\n    ...DefaultTheme.colors,\n    primary: '#1e40af',\n    secondary: '#059669',\n    accent: '#f59e0b',\n    background: '#f8fafc',\n    surface: '#ffffff',\n    text: '#1f2937',\n    onSurface: '#374151',\n    disabled: '#9ca3af',\n    placeholder: '#6b7280',\n    backdrop: 'rgba(0, 0, 0, 0.5)',\n    error: '#dc2626',\n    success: '#059669',\n    warning: '#f59e0b',\n    info: '#3b82f6',\n  },\n  fonts: {\n    ...DefaultTheme.fonts,\n    regular: {\n      fontFamily: 'System',\n      fontWeight: '400' as const,\n    },\n    medium: {\n      fontFamily: 'System',\n      fontWeight: '500' as const,\n    },\n    light: {\n      fontFamily: 'System',\n      fontWeight: '300' as const,\n    },\n    thin: {\n      fontFamily: 'System',\n      fontWeight: '100' as const,\n    },\n  },\n  roundness: 8,\n};\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,oBAAoB;AAEjD,OAAO,IAAMC,KAAK,GAAAC,aAAA,CAAAA,aAAA,KACbF,YAAY;EACfG,MAAM,EAAAD,aAAA,CAAAA,aAAA,KACDF,YAAY,CAACG,MAAM;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,SAAS;IACtBC,QAAQ,EAAE,oBAAoB;IAC9BC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;EAAS,EAChB;EACDC,KAAK,EAAAhB,aAAA,CAAAA,aAAA,KACAF,YAAY,CAACkB,KAAK;IACrBC,OAAO,EAAE;MACPC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd;EAAC,EACF;EACDI,SAAS,EAAE;AAAC,EACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}