{"name": "expo-linking", "version": "5.0.2", "description": "Create and open deep links universally", "main": "build/Linking.js", "types": "build/Linking.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-linking"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-linking"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/linking", "dependencies": {"@types/qs": "^6.9.7", "expo-constants": "~14.4.2", "invariant": "^2.2.4", "qs": "^6.11.0", "url-parse": "^1.5.9"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "jest": {"preset": "expo-module-scripts"}, "gitHead": "a4537e193188bdea5e6499cf1d799b44408ec255"}